﻿# SunnyUI.Common    
    
![SunnyUI.Net](https://camo.githubusercontent.com/fddb15a3839c30fbee3a0ac9635069789288e7e770b62ce31f14149e63743a18/68747470733a2f2f696d616765732e67697465652e636f6d2f75706c6f6164732f696d616765732f323032312f303332342f3231333631355f35343234306261395f3431363732302e706e67 "SunnyUI.png")    
    
- Blog:   https://www.cnblogs.com/yhuse    
- Gitee:  https://gitee.com/yhuse/SunnyUI    
- GitHub: https://github.com/yhuse/SunnyUI    
- Nuget:  https://www.nuget.org/packages/SunnyUI    
- 帮助文档目录: https://www.cnblogs.com/yhuse/p/SunnyUI_Menu.html    
    
欢迎交流，QQ群： 56829229  (SunnyUI技术交流群)，请给源码项目点个Star吧！！！    
    
#### 介绍    
- SunnyUI.Common 是基于.Net Framework4.0+、.Net8、.Net9 的开源工具类库、扩展类库。            
- 源码编译环境：    
  **VS2022 17.14**，安装.NetFramework4.0目标包的方法见：https://www.cnblogs.com/yhuse/p/15536393.html    
- 动态库应用环境：**VS2010**及以上均可：    
  1. **.NetFrameWork 项目**，从Nuget引用SunnyUI，或者直接引用已经编译好的Dll（SunnyUI.Common.dll）   
  2. **.Net8、.Net9 项目**，从Nuget引用SunnyUI.Common    
  3. 不支持（.Net Framework 4 **Client Profile**）。    
  4. 推荐通过Nuget安装：Install-Package SunnyUI.Common，或者通过Nuget搜索SunnyUI.Common安装。   
    
#### 支持开源
- 希望SunnyUI对您有用，您的支持也是SunnyUI开源的动力，SunnyUI有你更精彩！    
![感谢您的支持](https://camo.githubusercontent.com/0df607315cfda18b5afc5d6173400eb2a15b51ccffc81efb57c7cfe9824c8b6e/68747470733a2f2f696d616765732e67697465652e636f6d2f75706c6f6164732f696d616765732f323032312f303430392f3230313535385f39613039393361335f3431363732302e706e67 "SunnyUISupport.png")
