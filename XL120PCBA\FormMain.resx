﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="imageList_TestStatus.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>38, 19</value>
  </metadata>
  <data name="imageList_TestStatus.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAQ
        HQAAAk1TRnQBSQFMAgEBAwEAAfgBEAH4ARABGQEAARkBAAT/ASEBAAj/AUIBTQE2BwABNgMAASgDAAFk
        AwABGQMAAQEBAAEgBQABEAEnMgADXgHdA4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DUAGd
        /wBhAANgAd4DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DRwGA
        OAADLQFGAQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AxsBJlAA
        AwcBCgMFAQekAAOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8sAANXAbcBAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wM7AWU4AAMcAScCAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8BRwFBAWoB+QMEAQWQAAOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/yUA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AVEBbQFRAfcwAAJAAagB/QIA
        AdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wNc
        AeqIAAOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/HQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8qAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf+AAANf
        AfMDigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wNGAX8VAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AVEBbQFRAfciAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf94AAMHAQkDigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        EAADYQHmAQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AzsBZBoAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIA
        AdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIA
        AdYB/wIAAdYB/wIAAdYB/wNgAet0AAOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A1ABnQ0A
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8UAAM1AVYCAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8DSgGKAwQBBQIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wYAAdYB/wIA
        AdYB/wIAAdYB/wIAAdYB/wIAAdYB/wMEAQVwAAOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wgAA1UBsAEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8EAANZAfUBAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wMaASUSAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8IAAMFAQcCAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8OAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8BRwFBAWoB+XAAA4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/CQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wwA
        A2AB6wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/EgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/
        CAADBwEKAgAB1gH/AgAB1gH/DgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/bAADQAFw
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A2AB6wNbAcMDigH/A4oB/wOKAf8DWwHLA14B3QOKAf8DigH/
        A4oB/wNVAbUDigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/CQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8IAANXAbgIAANhAeYBAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8MAAMb
        ASYCAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CQAGoAf0IAAMJAQwOAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf9sAANcAd8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8IAAMJAQwDigH/Az4BaggAA1wB3wOKAf8MAAOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wkAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wQAAwEBAgEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wMRARcEAAFaAWEBWgHkAQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wwAAzMBUgIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIA
        AdYB/wIAAdYB/wIAAdYB/wNNAfoSAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8DBQEHaAADXAHqA4oB/wOKAf8DigH/A4oB/wOKAf8DigH/CAADBQEHA4oB/wMpAT8IAANb
        AcsDigH/DAADigH/A4oB/wOKAf8DigH/A4oB/wOKAf8JAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/BAABTAFNAUwBkQEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8DSgGKBAADXwHoAQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8MAAM3AVsCAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHW
        Af8MAAMKAQ0CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8DBwEJ
        aAADTgGZA4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A0wBkwMdASoDigH/A4oB/wOKAf8DKAE8Az4BawOK
        Af8DigH/A4oB/wMKAQ4DYQHhA4oB/wOKAf8DigH/A4oB/wOKAf8DigH/CQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8EAAMrAfwBAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wNdAfAEAANgAesBAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8MAAMgAS4CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHW
        Af8UAAMHAQoCAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf9wAAOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wkAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wQAAVwBZAFcAecBAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/DwABAQIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/w4A
        AdYB/wNNAfoIAAMGAQgCAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf9wAAOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wgAAV0BZAFdAewBAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8DHQEpAVoBYQFaAeQBAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8DLQFGEgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/DgAB1gH/AgAB1gH/
        AgAB1gH/AkABqAH9CAADBAEGAgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/cAADigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wNhAdwIAAMFAQcBAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/FAADUQGgAgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AwgBCwYAAdYB/wIAAdYB/wIA
        AdYB/wIAAdYB/wIAAdYB/wIAAdYB/wQAA0oBigIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wMbASZwAAMQ
        ARUDigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/EAABQAGTAUAB/QEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wNWAbYaAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CQAGoAf14AAOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wNgAd4VAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8cAAMWAR8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf+AAAOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/GAADEgEZ
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8kAAM3AVsCAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf+EAAMSARkDigH/A4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8gAAMY
        ASEBAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/LAADFgEf
        AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/
        AgAB1gH/AgAB1gH/jAADFwEgA4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/ywAAUABkwFAAf0BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wNhAeY4AAJQ
        AVEBnwIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wM1AVaYAAOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DXwHzNAADBQEH
        A2AB6wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wFVAVYBVQGx
        SwABAQMgAS4DNwFbAzMBUgMbASaoAAMQARUDigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/AwcBCf8AcQADTgGZA1wB6gNcAd8DQAFw/wBVAAFCAU0BPgcAAT4DAAEoAwABZAMAARkDAAEB
        AQABAQUAAZABARYAA/8BAAH/AQABPwb/AeAGAAH8AQABDwH/AcABPwH/AfwB/wHgBgAB+AEAAQcB/wEA
        AQ8B/wHAAQ8B4AYAAfABAAEDAf4BAAEHAf8BgAEHAeAGAAHgAQABAQH8AQABAwH/AQABAwHgBgABwAIA
        AfgBAAEBAf4BAAEBAeAGAAGAAgAB8AIAAfwCAAHgBgABgAIAAXACAAH4AQABIAFgBgABgAIAAWABCAEA
        AXgBMAFwAWAGAAGAAgABYAEcAQABeAEYAeABYAkAAWABNgEAAXABDQHAAWAGAAEBAYwB4AFgAUEBAAFw
        AQcBgAEgBgABAQGMAeABYAKAAXABBwEAASAJAAFhAQABQAFwAQ8BgAFgBgABgAIAAWABAAEgAXABHAHA
        AWAGAAGAAgABYAIAAXgBOAJgBgABgAIAAWACAAH4ARABIAFgBgABgAIAAfACAAH8AgAB4AYAAcACAAH4
        AQABAQH8AQABAQHgBgAB4AEAAQEB+AEAAQMB/gEAAQMB4AYAAeABAAEDAfwBAAEHAf8BAAEHAeAGAAHw
        AQABBwH/AQABDwH/AcABDwHgBgAB/AEAAQ8B/wGAAT8B/wHwAX8B4AYAAf4BAAEfBv8B4AYAAf8B4Qf/
        AeAGAAs=
</value>
  </data>
  <metadata name="timer_ShowTime.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>221, 25</value>
  </metadata>
</root>