﻿using Sunny.UI;

namespace C171PCBA.Config
{
    [ConfigFile("Config\\Setting.ini")]
    public class Setting : IniConfig<Setting>
    {
        [ConfigSection("App")] public string UserName { get; set; }
        [ConfigSection("App")] public string StationName { get; set; }
        public string SerialPortDevice { get; set; }
        public int SerialPortDeviceBaudRate { get; set; }
        public string SerialPortCurrent { get; set; }
        public int SerialPortCurrentBaudRate { get; set; }
        public string SerialPortScan { get; set; }
        public int SerialPortScanBaudRate { get; set; }
        public int SnLength { get; set; }

        public override void SetDefault()
        {
            base.SetDefault();
            UserName = "Mes账号";
            StationName = "测试站点";
            SerialPortDevice = "COM1";
            SerialPortDeviceBaudRate = 115200;
            SerialPortCurrent = "COM2";
            SerialPortCurrentBaudRate = 921600;
            SerialPortScan = "COM3";
            SerialPortScanBaudRate = 115200;
            SnLength = 28;
        }
    }
}