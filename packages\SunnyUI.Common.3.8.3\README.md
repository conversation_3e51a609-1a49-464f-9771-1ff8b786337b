﻿# SunnyUI.Common    
    
![SunnyUI.Net](https://images.gitee.com/uploads/images/2020/0627/205841_13e961a1_416720.png "SunnyUI.png")    
    
- Blog:   https://www.cnblogs.com/yhuse    
- Gitee:  https://gitee.com/yhuse/SunnyUI    
- GitHub: https://github.com/yhuse/SunnyUI    
- Nuget:  https://www.nuget.org/packages/SunnyUI    
- 帮助文档目录: https://www.cnblogs.com/yhuse/p/SunnyUI_Menu.html    
    
欢迎交流，QQ群： 56829229  (SunnyUI技术交流群)，请给源码项目点个Star吧！！！    
    
#### 介绍    
- SunnyUI.Common 是基于.Net Framework4.0+、.Net8、.Net9 的开源工具类库、扩展类库。            
- 源码编译环境：    
  **VS2022 17.4**，安装.NetFramework4.0目标包的方法见：https://www.cnblogs.com/yhuse/p/15536393.html    
- 动态库应用环境：**VS2010**及以上均可：    
  1. **.NetFrameWork项目**，从Nuget引用SunnyUI，或者直接引用已经编译好的Dll（SunnyUI.Common.dll）   
  2. **.NetCore、.Net5、.Net6、.Net7项目**，从Nuget引用SunnyUI.Common    
  3. 不支持（.Net Framework 4 **Client Profile**）。    
  4. 推荐通过Nuget安装：Install-Package SunnyUI.Common，或者通过Nuget搜索SunnyUI.Common安装。   
    
#### 支持开源
- 希望SunnyUI对您有用，您的支持也是SunnyUI开源的动力，SunnyUI有你更精彩！    
![感谢您的支持](https://images.gitee.com/uploads/images/2020/0524/233620_6685fbbf_416720.png "SunnyUISupport.png")
