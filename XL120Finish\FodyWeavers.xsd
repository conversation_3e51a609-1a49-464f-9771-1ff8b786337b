﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <!-- This file was generated by Fody. Manual changes to this file will be lost when your project is rebuilt. -->
  <xs:element name="Weavers">
    <xs:complexType>
      <xs:all>
        <xs:element name="Costura" minOccurs="0" maxOccurs="1">
          <xs:complexType>
            <xs:all>
              <xs:element minOccurs="0" maxOccurs="1" name="ExcludeAssemblies" type="xs:string">
                <xs:annotation>
                  <xs:documentation>A list of assembly names to exclude from the default action of "embed all Copy Local references", delimited with line breaks</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element minOccurs="0" maxOccurs="1" name="IncludeAssemblies" type="xs:string">
                <xs:annotation>
                  <xs:documentation>A list of assembly names to include from the default action of "embed all Copy Local references", delimited with line breaks.</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element minOccurs="0" maxOccurs="1" name="ExcludeRuntimeAssemblies" type="xs:string">
                <xs:annotation>
                  <xs:documentation>A list of runtime assembly names to exclude from the default action of "embed all Copy Local references", delimited with line breaks</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element minOccurs="0" maxOccurs="1" name="IncludeRuntimeAssemblies" type="xs:string">
                <xs:annotation>
                  <xs:documentation>A list of runtime assembly names to include from the default action of "embed all Copy Local references", delimited with line breaks.</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element minOccurs="0" maxOccurs="1" name="Unmanaged32Assemblies" type="xs:string">
                <xs:annotation>
                  <xs:documentation>Obsolete, use UnmanagedWinX86Assemblies instead</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element minOccurs="0" maxOccurs="1" name="UnmanagedWinX86Assemblies" type="xs:string">
                <xs:annotation>
                  <xs:documentation>A list of unmanaged X86 (32 bit) assembly names to include, delimited with line breaks.</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element minOccurs="0" maxOccurs="1" name="Unmanaged64Assemblies" type="xs:string">
                <xs:annotation>
                  <xs:documentation>Obsolete, use UnmanagedWinX64Assemblies instead.</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element minOccurs="0" maxOccurs="1" name="UnmanagedWinX64Assemblies" type="xs:string">
                <xs:annotation>
                  <xs:documentation>A list of unmanaged X64 (64 bit) assembly names to include, delimited with line breaks.</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element minOccurs="0" maxOccurs="1" name="UnmanagedWinArm64Assemblies" type="xs:string">
                <xs:annotation>
                  <xs:documentation>A list of unmanaged Arm64 (64 bit) assembly names to include, delimited with line breaks.</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element minOccurs="0" maxOccurs="1" name="PreloadOrder" type="xs:string">
                <xs:annotation>
                  <xs:documentation>The order of preloaded assemblies, delimited with line breaks.</xs:documentation>
                </xs:annotation>
              </xs:element>
            </xs:all>
            <xs:attribute name="CreateTemporaryAssemblies" type="xs:boolean">
              <xs:annotation>
                <xs:documentation>This will copy embedded files to disk before loading them into memory. This is helpful for some scenarios that expected an assembly to be loaded from a physical file.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="IncludeDebugSymbols" type="xs:boolean">
              <xs:annotation>
                <xs:documentation>Controls if .pdbs for reference assemblies are also embedded.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="IncludeRuntimeReferences" type="xs:boolean">
              <xs:annotation>
                <xs:documentation>Controls if runtime assemblies are also embedded.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="UseRuntimeReferencePaths" type="xs:boolean">
              <xs:annotation>
                <xs:documentation>Controls whether the runtime assemblies are embedded with their full path or only with their assembly name.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="DisableCompression" type="xs:boolean">
              <xs:annotation>
                <xs:documentation>Embedded assemblies are compressed by default, and uncompressed when they are loaded. You can turn compression off with this option.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="DisableCleanup" type="xs:boolean">
              <xs:annotation>
                <xs:documentation>As part of Costura, embedded assemblies are no longer included as part of the build. This cleanup can be turned off.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="DisableEventSubscription" type="xs:boolean">
              <xs:annotation>
                <xs:documentation>The attach method no longer subscribes to the `AppDomain.AssemblyResolve` (.NET 4.x) and `AssemblyLoadContext.Resolving` (.NET 6.0+) events.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="LoadAtModuleInit" type="xs:boolean">
              <xs:annotation>
                <xs:documentation>Costura by default will load as part of the module initialization. This flag disables that behavior. Make sure you call CosturaUtility.Initialize() somewhere in your code.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="IgnoreSatelliteAssemblies" type="xs:boolean">
              <xs:annotation>
                <xs:documentation>Costura will by default use assemblies with a name like 'resources.dll' as a satellite resource and prepend the output path. This flag disables that behavior.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="ExcludeAssemblies" type="xs:string">
              <xs:annotation>
                <xs:documentation>A list of assembly names to exclude from the default action of "embed all Copy Local references", delimited with |</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="IncludeAssemblies" type="xs:string">
              <xs:annotation>
                <xs:documentation>A list of assembly names to include from the default action of "embed all Copy Local references", delimited with |.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="ExcludeRuntimeAssemblies" type="xs:string">
              <xs:annotation>
                <xs:documentation>A list of runtime assembly names to exclude from the default action of "embed all Copy Local references", delimited with |</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="IncludeRuntimeAssemblies" type="xs:string">
              <xs:annotation>
                <xs:documentation>A list of runtime assembly names to include from the default action of "embed all Copy Local references", delimited with |.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="Unmanaged32Assemblies" type="xs:string">
              <xs:annotation>
                <xs:documentation>Obsolete, use UnmanagedWinX86Assemblies instead</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="UnmanagedWinX86Assemblies" type="xs:string">
              <xs:annotation>
                <xs:documentation>A list of unmanaged X86 (32 bit) assembly names to include, delimited with |.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="Unmanaged64Assemblies" type="xs:string">
              <xs:annotation>
                <xs:documentation>Obsolete, use UnmanagedWinX64Assemblies instead</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="UnmanagedWinX64Assemblies" type="xs:string">
              <xs:annotation>
                <xs:documentation>A list of unmanaged X64 (64 bit) assembly names to include, delimited with |.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="UnmanagedWinArm64Assemblies" type="xs:string">
              <xs:annotation>
                <xs:documentation>A list of unmanaged Arm64 (64 bit) assembly names to include, delimited with |.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
            <xs:attribute name="PreloadOrder" type="xs:string">
              <xs:annotation>
                <xs:documentation>The order of preloaded assemblies, delimited with |.</xs:documentation>
              </xs:annotation>
            </xs:attribute>
          </xs:complexType>
        </xs:element>
      </xs:all>
      <xs:attribute name="VerifyAssembly" type="xs:boolean">
        <xs:annotation>
          <xs:documentation>'true' to run assembly verification (PEVerify) on the target assembly after all weavers have been executed.</xs:documentation>
        </xs:annotation>
      </xs:attribute>
      <xs:attribute name="VerifyIgnoreCodes" type="xs:string">
        <xs:annotation>
          <xs:documentation>A comma-separated list of error codes that can be safely ignored in assembly verification.</xs:documentation>
        </xs:annotation>
      </xs:attribute>
      <xs:attribute name="GenerateXsd" type="xs:boolean">
        <xs:annotation>
          <xs:documentation>'false' to turn off automatic generation of the XML Schema file.</xs:documentation>
        </xs:annotation>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
</xs:schema>