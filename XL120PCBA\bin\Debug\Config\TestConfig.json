{"Project": "XL120_PCBA", "Version": "1.0.0.0", "UpdateTime": "2025-07-28 10:00:00", "Data": [{"Name": "等待设备连接", "CmdStr": "", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "fix_connect", "WaitTime": 1000, "WaitCount": 30, "Retry": 1, "SleepTime": 0, "Remark": "", "Params": [{"Name": "等待设备连接", "IsEnabled": true, "RegexPattern": "fix_connect", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "fix_connect", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "检测接地电压Input22", "CmdStr": "1A A1 40 00 00", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V22", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "检测接地电压Input22", "IsEnabled": true, "RegexPattern": "Get AD_V22.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V22", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 50.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开33V供电(C128电源OUT1)1", "CmdStr": "5A A5 01 5A A5", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OPEN_POWER_OUT1", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开33V供电(C128电源OUT1)1", "IsEnabled": true, "RegexPattern": "OPEN_POWER_OUT1", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "OPEN_POWER_OUT1", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "检测TP10电压5V-Input1", "CmdStr": "1A A1 00 00 02", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V1", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "检测TP10电压5V-Input1", "IsEnabled": true, "RegexPattern": "Get AD_V1.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V1", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 2400.0, "MaxValue": 2600.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "检测TP11电压3V3-Input2", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "检测TP11电压3V3-Input2", "IsEnabled": true, "RegexPattern": "Get AD_V2.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 3100.0, "MaxValue": 3500.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "参数写入", "CmdStr": "<SPBSJ*N:91325280001>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "参数写入", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "参数查询", "CmdStr": "<CKBSJ>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "参数查询", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "版本查询", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "UBoot版本", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "硬件版本", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "固件版本", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "测试LB-电压输出TP12-Input3", "CmdStr": "1A A1 00 00 08", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V3", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "测试LB-电压输出", "IsEnabled": true, "RegexPattern": "Get AD_V3.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V3", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 2400.0, "MaxValue": 2600.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "一线通测试", "CmdStr": "<CKBSJ>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "一线通测试", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "ACC测试", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "ACC测试", "IsEnabled": true, "RegexPattern": "Get AD_V2.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 3100.0, "MaxValue": 3500.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "喇叭测试", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "喇叭测试", "IsEnabled": true, "RegexPattern": "Get AD_V2.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 3100.0, "MaxValue": 3500.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "LBKEY按键测试", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "LBKEY按键测试", "IsEnabled": true, "RegexPattern": "Get AD_V2.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 3100.0, "MaxValue": 3500.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "点亮全部灯珠", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "点亮全部灯珠", "IsEnabled": true, "RegexPattern": "Get AD_V2.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 3100.0, "MaxValue": 3500.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "待机功耗(全亮灯)", "CmdStr": "7A A7 01 7A A7", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 8, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "待机功耗(全亮灯)", "IsEnabled": true, "RegexPattern": "ACK.CURRENT.*uA", "CutTextType": 3, "CutTextFlag1": "ACK+CURRENT:", "CutTextFlag2": "uA", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 200.0, "Unit": "uA", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "关闭全部灯珠", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "关闭全部灯珠", "IsEnabled": true, "RegexPattern": "Get AD_V2.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 3100.0, "MaxValue": 3500.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "待机功耗(不亮灯)", "CmdStr": "7A A7 01 7A A7", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 8, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "待机功耗(不亮灯)", "IsEnabled": true, "RegexPattern": "ACK.CURRENT.*uA", "CutTextType": 3, "CutTextFlag1": "ACK+CURRENT:", "CutTextFlag2": "uA", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 200.0, "Unit": "uA", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "休眠功耗", "CmdStr": "7A A7 01 7A A7", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 8, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "休眠功耗", "IsEnabled": true, "RegexPattern": "ACK.CURRENT.*uA", "CutTextType": 3, "CutTextFlag1": "ACK+CURRENT:", "CutTextFlag2": "uA", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 200.0, "Unit": "uA", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "MES过站", "CmdStr": "", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "", "WaitTime": 250, "WaitCount": 4, "Retry": 1, "SleepTime": 0, "Remark": "", "Params": [{"Name": "MES过站", "IsEnabled": false, "RegexPattern": "^[A-Za-z]+", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "True", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 10.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}]}