// This file is used by Code Analysis to maintain SuppressMessage attributes that are applied to this project.
// Project-level suppressions either have no target or are given a specific target and scoped to a namespace, type,
// member, etc.
//
// To add a suppression to this file, right-click the message in the Code Analysis results, point to "Suppress Message",
// and click "In Suppression File". You do not need to add suppressions to this file manually.

using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Style", "IDE0031:Use null propagation", Justification = ".NET Framework / Core compatibility")]
[assembly: SuppressMessage("Style", "IDE0044:Add readonly modifier", Justification = "P/Invoke", Scope = "namespaceanddescendants", Target = "~N:RJCP.IO.Ports.Native")]
[assembly: SuppressMessage("Style", "IDE0056:Use index operator", Justification = ".NET Framework / Core compatibility")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "P/Invoke", Scope = "namespaceanddescendants", Target = "~N:RJCP.IO.Ports.Native")]
[assembly: SuppressMessage("Major Code Smell", "S1066:Collapsible \"if\" statements should be merged", Justification = "No benefit")]
[assembly: SuppressMessage("Major Code Smell", "S2933:Fields that are only assigned in the constructor should be \"readonly\"", Justification = "<Pending>")]
[assembly: SuppressMessage("Minor Code Smell", "S4136:Method overloads should be grouped together", Justification = "No benefit")]
