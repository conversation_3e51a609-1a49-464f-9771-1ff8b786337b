<?xml version="1.0"?>
<doc>
    <assembly>
        <name>FreeSql</name>
    </assembly>
    <members>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.Name">
            <summary>
            数据库列名
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.OldName">
            <summary>
            指定数据库旧的列名，修改实体属性命名时，同时设置此参数为修改之前的值，CodeFirst才可以正确修改数据库字段；否则将视为【新增字段】
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.DbType">
            <summary>
            数据库类型，如： varchar(255) <para></para>
            字符串长度，可使用特性 [MaxLength(255)]
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.IsPrimary">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.IsIdentity">
            <summary>
            自增标识
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.IsNullable">
            <summary>
            是否可DBNull
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.IsIgnore">
            <summary>
            忽略此列，不迁移、不插入
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.IsVersion">
            <summary>
            设置行锁（乐观锁）版本号，每次更新累加版本号，若更新整个实体时会附带当前的版本号判断（修改失败时抛出异常）
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.MapType">
            <summary>
            类型映射，除了可做基本的类型映射外，特别介绍的功能：<para></para>
            1、将 enum 属性映射成 typeof(string)<para></para>
            2、将 对象 属性映射成 typeof(string)，请安装扩展包 FreeSql.Extensions.JsonMap
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.Position">
            <summary>
            创建表时字段的位置（场景：实体继承后设置字段顺序），规则如下：
            <para></para>
            &gt;0时排前面，1,2,3...
            <para></para>
            =0时排中间(默认)
            <para></para>
            &lt;0时排后面，...-3,-2,-1
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.CanInsert">
            <summary>
            该字段是否可以插入，默认值true，指定为false插入时该字段会被忽略
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.CanUpdate">
            <summary>
            该字段是否可以更新，默认值true，指定为false更新时该字段会被忽略
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.ServerTime">
            <summary>
            标记属性为数据库服务器时间(utc/local)，在插入的时候使用类似 getdate() 执行
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.StringLength">
            <summary>
            设置长度，针对 string/byte[] 类型避免 DbType 的繁琐设置<para></para>
            提示：也可以使用 [MaxLength(100)]<para></para>
            ---<para></para>
            StringLength = 100 时，对应 DbType：<para></para>
            MySql -> varchar(100)<para></para>
            SqlServer -> nvarchar(100)<para></para>
            PostgreSQL -> varchar(100)<para></para>
            Oracle -> nvarchar2(100)<para></para>
            Sqlite -> nvarchar(100)<para></para>
            ---<para></para>
            StringLength &lt; 0 时，对应 DbType：<para></para>
            MySql -> text (StringLength = -2 时，对应 longtext)<para></para>
            SqlServer -> nvarchar(max)<para></para>
            PostgreSQL -> text<para></para>
            Oracle -> nclob<para></para>
            Sqlite -> text<para></para>
            v1.6.0+ byte[] 支持设置 StringLength
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.InsertValueSql">
            <summary>
            执行 Insert 方法时使用此值<para></para>
            注意：如果是 getdate() 这种请可考虑使用 ServerTime，因为它对数据库间作了适配
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.Precision">
            <summary>
            decimal/numeric 类型的长度
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.Scale">
            <summary>
            decimal/numeric 类型的小数位长度
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.RewriteSql">
            <summary>
            重写功能<para></para>
            比如：[Column(RewriteSql = &quot;geography::STGeomFromText({0},4236)&quot;)]<para></para>
            插入：INSERT INTO [table]([geo]) VALUES(geography::STGeomFromText('...',4236))<para></para>
            提示：更新也生效
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ColumnAttribute.RereadSql">
            <summary>
            重读功能<para></para>
            比如：[Column(RereadSql = &quot;{0}.STAsText()&quot;)]<para></para>
            或者：[Column(RereadSql = &quot;{geo}.STAsText()&quot;)]<para></para>
            查询：SELECT a.[id], a.[geo].STAsText() FROM [table] a
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.Name(System.String)">
            <summary>
            数据库列名
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.OldName(System.String)">
            <summary>
            指定数据库旧的列名，修改实体属性命名时，同时设置此参数为修改之前的值，CodeFirst才可以正确修改数据库字段；否则将视为【新增字段】
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.DbType(System.String)">
            <summary>
            数据库类型，如： varchar(255)
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.IsPrimary(System.Boolean)">
            <summary>
            主键
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.IsIdentity(System.Boolean)">
            <summary>
            自增标识
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.IsNullable(System.Boolean)">
            <summary>
            是否可DBNull
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.IsIgnore(System.Boolean)">
            <summary>
            忽略此列，不迁移、不插入
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.IsVersion(System.Boolean)">
            <summary>
            乐观锁
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.MapType(System.Type)">
            <summary>
            类型映射，比如：可将 enum 属性映射成 typeof(string)
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.Position(System.Int16)">
            <summary>
            创建表时字段位置，规则如下：
            <para></para>
            &gt;0时排前面
            <para></para>
            =0时排中间(默认)
            <para></para>
            &lt;0时排后面
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.CanInsert(System.Boolean)">
            <summary>
            该字段是否可以插入，默认值true，指定为false插入时该字段会被忽略
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.CanUpdate(System.Boolean)">
            <summary>
            该字段是否可以更新，默认值true，指定为false更新时该字段会被忽略
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.ServerTime(System.DateTimeKind)">
            <summary>
            标记属性为数据库服务器时间(utc/local)，在插入的时候使用类似 getdate() 执行
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.StringLength(System.Int32)">
            <summary>
            设置长度，针对 string 类型避免 DbType 的繁琐设置<para></para>
            ---<para></para>
            StringLength = 100 时，对应 DbType：<para></para>
            MySql -> varchar(100)<para></para>
            SqlServer -> nvarchar(100)<para></para>
            PostgreSQL -> varchar(100)<para></para>
            Oracle -> nvarchar2(100)<para></para>
            Sqlite -> nvarchar(100)<para></para>
            ---<para></para>
            StringLength = -1 时，对应 DbType：<para></para>
            MySql -> text<para></para>
            SqlServer -> nvarchar(max)<para></para>
            PostgreSQL -> text<para></para>
            Oracle -> nvarchar2(4000)<para></para>
            Sqlite -> text<para></para>
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.InsertValueSql(System.String)">
            <summary>
            执行 Insert 方法时使用此值<para></para>
            注意：如果是 getdate() 这种请可考虑使用 ServerTime，因为它对数据库间作了适配
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.Precision(System.Int32,System.Int32)">
            <summary>
            decimal/numeric 类型的长度/小数位长度
            </summary>
            <param name="precision">总长度</param>
            <param name="scale">小数位长度</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.RewriteSql(System.String)">
            <summary>
            重写功能<para></para>
            比如：[Column(RewriteSql = &quot;geography::STGeomFromText({0},4236)&quot;)]<para></para>
            插入：INSERT INTO [table]([geo]) VALUES(geography::STGeomFromText('...',4236))<para></para>
            提示：更新也生效
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.DataAnnotations.ColumnFluent.RereadSql(System.String)">
            <summary>
            重读功能<para></para>
            比如：[Column(RereadSql = &quot;{0}.STAsText()&quot;)]<para></para>
            或者：[Column(RereadSql = &quot;{geo}.STAsText()&quot;)]<para></para>
            查询：SELECT a.[id], a.[geo].STAsText() FROM [table] a
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="T:FreeSql.DataAnnotations.ExpressionCallAttribute">
            <summary>
            自定义表达式函数解析<para></para>
            注意：请使用静态方法、或者在类上标记
            </summary>
        </member>
        <member name="T:FreeSql.DataAnnotations.RawValueAttribute">
            <summary>
            自定义表达式函数解析的时候，指定参数不解析 SQL，而是直接传进来
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ExpressionCallContext.DataType">
            <summary>
            数据库类型，可用于适配多种数据库环境
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ExpressionCallContext.ParsedContent">
            <summary>
            已解析的表达式中参数内容
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ExpressionCallContext.RawExpression">
            <summary>
            表达式原始值
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ExpressionCallContext.DbParameter">
            <summary>
            主对象的参数化对象，可重塑其属性
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ExpressionCallContext.UserParameters">
            <summary>
            可附加参数化对象<para></para>
            注意：本属性只有 Where 的表达式解析才可用
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ExpressionCallContext.FormatSql">
            <summary>
            将 c# 对象转换为 SQL
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.ExpressionCallContext.Result">
            <summary>
            返回表达式函数表示的 SQL 字符串
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.ExpressionCallContext.IUtility.GetTableByEntity(System.Type)">
            <summary>
            获取实体元数据
            </summary>
            <param name="entityType"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.DataAnnotations.ExpressionCallContext.IUtility.ParseExpression(System.Linq.Expressions.Expression)">
            <summary>
            解析表达式
            </summary>
            <param name="exp"></param>
            <returns></returns>
        </member>
        <member name="P:FreeSql.DataAnnotations.ExpressionCallContext.IUtility.CommonUtils">
            <summary>
            (非公开)内部公共工具类方法
            </summary>
        </member>
        <member name="T:FreeSql.DataAnnotations.IndexAttribute">
            <summary>
            索引设置，如：[Index("{tablename}_idx_01", "name")]
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.IndexAttribute.#ctor(System.String,System.String)">
            <summary>
            索引设置，如：[Index("{tablename}_idx_01", "name")]
            </summary>
            <param name="name">索引名<para></para>v1.7.0 增加占位符 {TableName} 表名区分索引名 （解决 AsTable 分表 CodeFirst 导致索引名重复的问题）</param>
            <param name="fields">索引字段，为属性名以逗号分隔，如：Create_time ASC, Title ASC</param>
        </member>
        <member name="M:FreeSql.DataAnnotations.IndexAttribute.#ctor(System.String,System.String,System.Boolean)">
            <summary>
            索引设置，如：[Index("{tablename}_idx_01", "name", true)]
            </summary>
            <param name="name">索引名<para></para>v1.7.0 增加占位符 {TableName} 表名区分索引名 （解决 AsTable 分表 CodeFirst 导致索引名重复的问题）</param>
            <param name="fields">索引字段，为属性名以逗号分隔，如：Create_time ASC, Title ASC</param>
            <param name="isUnique">是否唯一</param>
        </member>
        <member name="P:FreeSql.DataAnnotations.IndexAttribute.Name">
            <summary>
            索引名<para></para>
            v1.7.0 增加占位符 {TableName} 表名区分索引名 （解决 AsTable 分表 CodeFirst 导致索引名重复的问题）
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.IndexAttribute.Fields">
            <summary>
            索引字段，为属性名以逗号分隔，如：Create_time ASC, Title ASC
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.IndexAttribute.IsUnique">
            <summary>
            是否唯一
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.IndexAttribute.IndexMethod">
            <summary>
            索引类型<para></para>
            暂时只有 FreeSql.Provider.PostgreSQL 有效
            </summary>
        </member>
        <member name="T:FreeSql.DataAnnotations.IndexMethod">
            <summary>
            暂时只有 FreeSql.Provider.PostgreSQL 有效
            </summary>
        </member>
        <member name="T:FreeSql.DataAnnotations.NavigateAttribute">
            <summary>
            OneToOne：[Navigate(nameof(Primary))] &lt;-&gt; (缺省)外表.Primary<para></para>
            ManyToOne：Topic.cs 文件 [Navigate(nameof(Topic.CategoryId))] &lt;-&gt; (缺省)Category.Id<para></para>
            _________________public Category Category { get; set; }<para></para>
            OneToMany：Category.cs 文件 (缺省)Category.Id &lt;-&gt; [Navigate(nameof(Topic.CategoryId))]<para></para>
            _________________public List&lt;Topic&gt; Topics { get; set; }<para></para>
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.NavigateAttribute.Bind">
            <summary>
            OneToOne：[Navigate(nameof(Primary))] &lt;-&gt; (缺省)外表.Primary<para></para>
            ManyToOne：Topic.cs 文件 [Navigate(nameof(Topic.CategoryId))] &lt;-&gt; (缺省)Category.Id<para></para>
            _________________public Category Category { get; set; }<para></para>
            OneToMany：Category.cs 文件 (缺省)Category.Id &lt;-&gt; [Navigate(nameof(Topic.CategoryId))]<para></para>
            _________________public List&lt;Topic&gt; Topics { get; set; }<para></para>
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.NavigateAttribute.TempPrimary">
            <summary>
            与非主键进行关联，仅支持 OneToMany、ManyToOne<para></para>
            使用方法参考 Bind 属性
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.NavigateAttribute.ManyToMany">
            <summary>
            手工绑定 ManyToMany 导航关系
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.NavigateAttribute.#ctor(System.String)">
            <summary>
            OneToOne：[Navigate(nameof(Primary))] &lt;-&gt; (缺省)外表.Primary<para></para>
            ManyToOne：Topic.cs 文件 [Navigate(nameof(Topic.CategoryId))] &lt;-&gt; (缺省)Category.Id<para></para>
            _________________public Category Category { get; set; }<para></para>
            OneToMany：Category.cs 文件 (缺省)Category.Id &lt;-&gt; [Navigate(nameof(Topic.CategoryId))]<para></para>
            _________________public List&lt;Topic&gt; Topics { get; set; }<para></para>
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.OraclePrimaryKeyNameAttribute.Name">
            <summary>
            主键名
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.TableAttribute.Name">
            <summary>
            数据库表名
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.TableAttribute.OldName">
            <summary>
            指定数据库旧的表名，修改实体命名时，同时设置此参数为修改之前的值，CodeFirst才可以正确修改数据库表；否则将视为【创建新表】
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.TableAttribute.DisableSyncStructure">
            <summary>
            禁用 CodeFirst 同步结构迁移
            </summary>
        </member>
        <member name="P:FreeSql.DataAnnotations.TableAttribute.AsTable">
            <summary>
            格式：属性名=开始时间(递增)<para></para>
            按年分表：[Table(Name = "log_{yyyy}", AsTable = "create_time=2022-1-1(1 year)")]<para></para>
            按月分表：[Table(Name = "log_{yyyyMM}", AsTable = "create_time=2022-5-1(1 month)")]<para></para>
            按日分表：[Table(Name = "log_{yyyyMMdd}", AsTable = "create_time=2022-5-1(5 day)")]<para></para>
            按时分表：[Table(Name = "log_{yyyyMMddHH}", AsTable = "create_time=2022-5-1(6 hour)")]<para></para>
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.DateTimeAsTableImpl.GetTableNamesBySqlWhere(System.String,System.Collections.Generic.List{System.Data.Common.DbParameter},FreeSql.Internal.Model.SelectTableInfo,FreeSql.Internal.CommonUtils)">
            <summary>
            可以匹配以下条件（支持参数化）：<para></para>
            `field` BETWEEN '2022-01-01 00:00:00' AND '2022-03-01 00:00:00'<para></para>
            `field` &gt; '2022-01-01 00:00:00' AND `field` &lt; '2022-03-01 00:00:00'<para></para>
            `field` &gt; '2022-01-01 00:00:00' AND `field` &lt;= '2022-03-01 00:00:00'<para></para>
            `field` &gt;= '2022-01-01 00:00:00' AND `field` &lt; '2022-03-01 00:00:00'<para></para>
            `field` &gt;= '2022-01-01 00:00:00' AND `field` &lt;= '2022-03-01 00:00:00'<para></para>
            `field` &gt; '2022-01-01 00:00:00'<para></para>
            `field` &gt;= '2022-01-01 00:00:00'<para></para>
            `field` &lt; '2022-01-01 00:00:00'<para></para>
            `field` &lt;= '2022-01-01 00:00:00'<para></para>
            </summary>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:FreeSql.DataAnnotations.TableFluent.Name(System.String)">
            <summary>
            数据库表名
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.TableFluent.OldName(System.String)">
            <summary>
            指定数据库旧的表名，修改实体命名时，同时设置此参数为修改之前的值，CodeFirst才可以正确修改数据库表；否则将视为【创建新表】
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.TableFluent.DisableSyncStructure(System.Boolean)">
            <summary>
            禁用 CodeFirst 同步结构迁移
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.TableFluent.AsTable(System.String)">
            <summary>
            格式：属性名=开始时间(递增)<para></para>
            按年分表：[Table(Name = "log_{yyyy}", AsTable = "create_time=2022-1-1(1 year)")]<para></para>
            按月分表：[Table(Name = "log_{yyyyMM}", AsTable = "create_time=2022-5-1(1 month)")]<para></para>
            按日分表：[Table(Name = "log_{yyyyMMdd}", AsTable = "create_time=2022-5-1(5 day)")]<para></para>
            按时分表：[Table(Name = "log_{yyyyMMddHH}", AsTable = "create_time=2022-5-1(6 hour)")]<para></para>
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.TableFluent.Navigate(System.String,System.String,System.Type)">
            <summary>
            导航关系Fluent，与 NavigateAttribute 对应
            </summary>
            <param name="proto"></param>
            <param name="bind"></param>
            <param name="manyToMany">多对多关系的中间实体类型</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.DataAnnotations.TableFluent.Index(System.String,System.String,System.Boolean)">
            <summary>
            设置实体的索引
            </summary>
            <param name="name">索引名</param>
            <param name="fields">索引字段，为属性名以逗号分隔，如：Create_time ASC, Title ASC</param>
            <param name="isUnique">是否唯一</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.DataAnnotations.TableFluent`1.Name(System.String)">
            <summary>
            数据库表名
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.TableFluent`1.OldName(System.String)">
            <summary>
            指定数据库旧的表名，修改实体命名时，同时设置此参数为修改之前的值，CodeFirst才可以正确修改数据库表；否则将视为【创建新表】
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.TableFluent`1.DisableSyncStructure(System.Boolean)">
            <summary>
            禁用 CodeFirst 同步结构迁移
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.TableFluent`1.AsTable(System.String)">
            <summary>
            格式：属性名=开始时间(递增)<para></para>
            按年分表：[Table(Name = "log_{yyyy}", AsTable = "create_time=2022-1-1(1 year)")]<para></para>
            按月分表：[Table(Name = "log_{yyyyMM}", AsTable = "create_time=2022-5-1(1 month)")]<para></para>
            按日分表：[Table(Name = "log_{yyyyMMdd}", AsTable = "create_time=2022-5-1(5 day)")]<para></para>
            按时分表：[Table(Name = "log_{yyyyMMddHH}", AsTable = "create_time=2022-5-1(6 hour)")]<para></para>
            </summary>
        </member>
        <member name="M:FreeSql.DataAnnotations.TableFluent`1.Navigate``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.String,System.Type)">
            <summary>
            导航关系Fluent，与 NavigateAttribute 对应
            </summary>
            <typeparam name="TProto"></typeparam>
            <param name="proto"></param>
            <param name="bind"></param>
            <param name="manyToMany">多对多关系的中间实体类型</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.DataAnnotations.TableFluent`1.Index(System.String,System.String,System.Boolean)">
            <summary>
            设置实体的索引
            </summary>
            <param name="name">索引名</param>
            <param name="fields">索引字段，为属性名以逗号分隔，如：Create_time ASC, Title ASC</param>
            <param name="isUnique">是否唯一</param>
            <returns></returns>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.Table">
            <summary>
            所属表
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.Name">
            <summary>
            列名
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.CsType">
            <summary>
            映射到 C# 类型
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.DbType">
            <summary>
            数据库枚举类型int值
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.DbTypeText">
            <summary>
            数据库类型，字符串，varchar
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.DbTypeTextFull">
            <summary>
            数据库类型，字符串，varchar(255)
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.MaxLength">
            <summary>
            最大长度
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.Precision">
            <summary>
            暂支持 SqlServer/MySql（其他数据库待补充）
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.Scale">
            <summary>
            暂支持 SqlServer/MySql（其他数据库待补充）
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.IsPrimary">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.IsIdentity">
            <summary>
            自增标识
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.IsNullable">
            <summary>
            是否可DBNull
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.Coment">
            <summary>
            备注，早期编码时少按了一个字母，请使用 Comment
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.Comment">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.DefaultValue">
            <summary>
            数据库默认值
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbColumnInfo.Position">
            <summary>
            字段位置
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbEnumInfo.Name">
            <summary>
            枚举类型标识
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbEnumInfo.Labels">
            <summary>
            枚举项
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbTableInfo.Id">
            <summary>
            唯一标识
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbTableInfo.Schema">
            <summary>
            SqlServer下是Owner、PostgreSQL下是Schema、MySql下是数据库名
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbTableInfo.Name">
            <summary>
            表名
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbTableInfo.Comment">
            <summary>
            表备注，SqlServer下是扩展属性 MS_Description
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbTableInfo.Type">
            <summary>
            表/视图
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbTableInfo.Columns">
            <summary>
            列
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbTableInfo.Identitys">
            <summary>
            自增列
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbTableInfo.Primarys">
            <summary>
            主键/组合
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbTableInfo.UniquesDict">
            <summary>
            唯一键/组合
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbTableInfo.IndexesDict">
            <summary>
            索引/组合
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbTableInfo.ForeignsDict">
            <summary>
            外键
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbTypeInfo.Name">
            <summary>
            类型标识
            </summary>
        </member>
        <member name="P:FreeSql.DatabaseModel.DbTypeInfo.Labels">
            <summary>
            枚举项
            </summary>
        </member>
        <member name="F:FreeSql.DataType.Odbc">
            <summary>
            通用的 Odbc 访问数据库 https://freesql.net/guide/freesql-provider-odbc.html
            </summary>
        </member>
        <member name="F:FreeSql.DataType.MsAccess">
            <summary>
            Microsoft Office Access 是由微软发布的关联式数据库管理系统
            </summary>
        </member>
        <member name="F:FreeSql.DataType.Dameng">
            <summary>
            武汉达梦数据库有限公司，基于 DmProvider.dll 的实现
            </summary>
        </member>
        <member name="F:FreeSql.DataType.ShenTong">
            <summary>
             天津神舟通用数据技术有限公司，基于 System.Data.OscarClient.dll 的实现
            </summary>
        </member>
        <member name="F:FreeSql.DataType.KingbaseES">
            <summary>
            北京人大金仓信息技术股份有限公司，基于 Kdbndp.dll 的实现
            </summary>
        </member>
        <member name="F:FreeSql.DataType.Firebird">
            <summary>
            Firebird 是一个跨平台的关系数据库，能作为多用户环境下的数据库服务器运行，也提供嵌入式数据库的实现
            </summary>
        </member>
        <member name="F:FreeSql.DataType.Custom">
            <summary>
            自定义适配器，访问任何数据库 https://freesql.net/guide/freesql-provider-custom.html
            </summary>
        </member>
        <member name="F:FreeSql.DataType.GBase">
            <summary>
            天津南大通用数据技术股份有限公司成立于2004年,是国产数据库、大数据领域的知名企业，基于 Odbc 的实现
            </summary>
        </member>
        <member name="F:FreeSql.DataType.Xugu">
            <summary>
            虚谷
            </summary>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.GetIFreeSql(System.Data.IDbConnection)">
            <summary>
            获取 IDbConnection 对应的 IFreeSql 实例
            </summary>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Insert``1(System.Data.IDbConnection)">
            <summary>
            插入数据
            </summary>
            <typeparam name="T1"></typeparam>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Insert``1(System.Data.IDbConnection,``0)">
            <summary>
            插入数据，传入实体
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Insert``1(System.Data.IDbConnection,``0[])">
            <summary>
            插入数据，传入实体数组
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Insert``1(System.Data.IDbConnection,System.Collections.Generic.List{``0})">
            <summary>
            插入数据，传入实体集合
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Insert``1(System.Data.IDbConnection,System.Collections.Generic.IEnumerable{``0})">
            <summary>
            插入数据，传入实体集合
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.InsertOrUpdate``1(System.Data.IDbConnection)">
            <summary>
            插入或更新数据，此功能依赖数据库特性（低版本可能不支持），参考如下：<para></para>
            MySql 5.6+: on duplicate key update<para></para>
            PostgreSQL 9.4+: on conflict do update<para></para>
            SqlServer 2008+: merge into<para></para>
            Oracle 11+: merge into<para></para>
            Sqlite: replace into<para></para>
            Firebird: merge into<para></para>
            达梦: merge into<para></para>
            人大金仓：on conflict do update<para></para>
            神通：merge into<para></para>
            MsAccess：不支持<para></para>
            注意区别：FreeSql.Repository 仓储也有 InsertOrUpdate 方法（不依赖数据库特性）
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Update``1(System.Data.IDbConnection)">
            <summary>
            修改数据
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Update``1(System.Data.IDbConnection,System.Object)">
            <summary>
            修改数据，传入动态条件，如：主键值 | new[]{主键值1,主键值2} | TEntity1 | new[]{TEntity1,TEntity2} | new{id=1}
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="dywhere">主键值、主键值集合、实体、实体集合、匿名对象、匿名对象集合</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``1(System.Data.IDbConnection)">
            <summary>
            查询数据
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``1(System.Data.IDbConnection,System.Object)">
            <summary>
            查询数据，传入动态条件，如：主键值 | new[]{主键值1,主键值2} | TEntity1 | new[]{TEntity1,TEntity2} | new{id=1}
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="dywhere">主键值、主键值集合、实体、实体集合、匿名对象、匿名对象集合</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Delete``1(System.Data.IDbConnection)">
            <summary>
            删除数据
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Delete``1(System.Data.IDbConnection,System.Object)">
            <summary>
            删除数据，传入动态条件，如：主键值 | new[]{主键值1,主键值2} | TEntity1 | new[]{TEntity1,TEntity2} | new{id=1}
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="dywhere">主键值、主键值集合、实体、实体集合、匿名对象、匿名对象集合</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``2(System.Data.IDbConnection)">
            <summary>
            多表查询
            </summary>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``3(System.Data.IDbConnection)">
            <summary>
            多表查询
            </summary>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``4(System.Data.IDbConnection)">
            <summary>
            多表查询
            </summary>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``5(System.Data.IDbConnection)">
            <summary>
            多表查询
            </summary>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``6(System.Data.IDbConnection)">
            <summary>
            多表查询
            </summary>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``7(System.Data.IDbConnection)">
            <summary>
            多表查询
            </summary>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``8(System.Data.IDbConnection)">
            <summary>
            多表查询
            </summary>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``9(System.Data.IDbConnection)">
            <summary>
            多表查询
            </summary>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``10(System.Data.IDbConnection)">
            <summary>
            多表查询
            </summary>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Insert``1(System.Data.IDbTransaction)">
            <summary>
            插入数据
            </summary>
            <typeparam name="T1"></typeparam>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Insert``1(System.Data.IDbTransaction,``0)">
            <summary>
            插入数据，传入实体
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Insert``1(System.Data.IDbTransaction,``0[])">
            <summary>
            插入数据，传入实体数组
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Insert``1(System.Data.IDbTransaction,System.Collections.Generic.List{``0})">
            <summary>
            插入数据，传入实体集合
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Insert``1(System.Data.IDbTransaction,System.Collections.Generic.IEnumerable{``0})">
            <summary>
            插入数据，传入实体集合
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.InsertOrUpdate``1(System.Data.IDbTransaction)">
            <summary>
            插入或更新数据，此功能依赖数据库特性（低版本可能不支持），参考如下：<para></para>
            MySql 5.6+: on duplicate key update<para></para>
            PostgreSQL 9.4+: on conflict do update<para></para>
            SqlServer 2008+: merge into<para></para>
            Oracle 11+: merge into<para></para>
            Sqlite: replace into<para></para>
            达梦: merge into<para></para>
            人大金仓：on conflict do update<para></para>
            神通：merge into<para></para>
            MsAccess：不支持<para></para>
            注意区别：FreeSql.Repository 仓储也有 InsertOrUpdate 方法（不依赖数据库特性）
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Update``1(System.Data.IDbTransaction)">
            <summary>
            修改数据
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Update``1(System.Data.IDbTransaction,System.Object)">
            <summary>
            修改数据，传入动态条件，如：主键值 | new[]{主键值1,主键值2} | TEntity1 | new[]{TEntity1,TEntity2} | new{id=1}
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="dywhere">主键值、主键值集合、实体、实体集合、匿名对象、匿名对象集合</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``1(System.Data.IDbTransaction)">
            <summary>
            查询数据
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``1(System.Data.IDbTransaction,System.Object)">
            <summary>
            查询数据，传入动态条件，如：主键值 | new[]{主键值1,主键值2} | TEntity1 | new[]{TEntity1,TEntity2} | new{id=1}
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="dywhere">主键值、主键值集合、实体、实体集合、匿名对象、匿名对象集合</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Delete``1(System.Data.IDbTransaction)">
            <summary>
            删除数据
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Delete``1(System.Data.IDbTransaction,System.Object)">
            <summary>
            删除数据，传入动态条件，如：主键值 | new[]{主键值1,主键值2} | TEntity1 | new[]{TEntity1,TEntity2} | new{id=1}
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="dywhere">主键值、主键值集合、实体、实体集合、匿名对象、匿名对象集合</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``2(System.Data.IDbTransaction)">
            <summary>
            多表查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``3(System.Data.IDbTransaction)">
            <summary>
            多表查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``4(System.Data.IDbTransaction)">
            <summary>
            多表查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``5(System.Data.IDbTransaction)">
            <summary>
            多表查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``6(System.Data.IDbTransaction)">
            <summary>
            多表查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``7(System.Data.IDbTransaction)">
            <summary>
            多表查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``8(System.Data.IDbTransaction)">
            <summary>
            多表查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``9(System.Data.IDbTransaction)">
            <summary>
            多表查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.AdoNetExtensions.Select``10(System.Data.IDbTransaction)">
            <summary>
            多表查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Extensions.EntityUtil.EntityUtilExtensions.GetEntityKeyString(IFreeSql,System.Type,System.Object,System.Boolean,System.String)">
            <summary>
            获取实体的主键值，以 "*|_,[,_|*" 分割，当任意一个主键属性无值时，返回 ""
            </summary>
            <param name="orm"></param>
            <param name="entityType"></param>
            <param name="entity"></param>
            <param name="genGuid">当Guid无值时，会生成有序的新值</param>
            <param name="splitString"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Extensions.EntityUtil.EntityUtilExtensions.GetEntityKeyValues(IFreeSql,System.Type,System.Object)">
            <summary>
            获取实体的主键值，多个主键返回数组
            </summary>
            <param name="orm"></param>
            <param name="entityType"></param>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Extensions.EntityUtil.EntityUtilExtensions.GetEntityValueWithPropertyName(IFreeSql,System.Type,System.Object,System.String)">
            <summary>
            获取实体的属性值
            </summary>
            <param name="orm"></param>
            <param name="entityType"></param>
            <param name="entity"></param>
            <param name="propertyName"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Extensions.EntityUtil.EntityUtilExtensions.GetEntityString(IFreeSql,System.Type,System.Object)">
            <summary>
            获取实体的所有数据，以 (1, 2, xxx) 的形式
            </summary>
            <param name="orm"></param>
            <param name="entityType"></param>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="F:FreeSql.Extensions.EntityUtil.EntityUtilExtensions._dicMapEntityValue">
            <summary>
            使用新实体的值，复盖旧实体的值
            </summary>
        </member>
        <member name="M:FreeSql.Extensions.EntityUtil.EntityUtilExtensions.MapEntityKeyValue(IFreeSql,System.Type,System.Object,System.Object)">
            <summary>
            使用新实体的主键值，复盖旧实体的主键值
            </summary>
        </member>
        <member name="M:FreeSql.Extensions.EntityUtil.EntityUtilExtensions.SetEntityIdentityValueWithPrimary(IFreeSql,System.Type,System.Object,System.Int64)">
            <summary>
            设置实体中主键内的自增字段值（若存在）
            </summary>
            <param name="orm"></param>
            <param name="entityType"></param>
            <param name="entity"></param>
            <param name="idtval"></param>
        </member>
        <member name="M:FreeSql.Extensions.EntityUtil.EntityUtilExtensions.GetEntityIdentityValueWithPrimary(IFreeSql,System.Type,System.Object)">
            <summary>
            获取实体中主键内的自增字段值（若存在）
            </summary>
            <param name="orm"></param>
            <param name="entityType"></param>
            <param name="entity"></param>
        </member>
        <member name="M:FreeSql.Extensions.EntityUtil.EntityUtilExtensions.ClearEntityPrimaryValueWithIdentityAndGuid(IFreeSql,System.Type,System.Object)">
            <summary>
            清除实体的主键值，将自增、Guid类型的主键值清除
            </summary>
            <param name="orm"></param>
            <param name="entityType"></param>
            <param name="entity"></param>
        </member>
        <member name="M:FreeSql.Extensions.EntityUtil.EntityUtilExtensions.ClearEntityPrimaryValueWithIdentity(IFreeSql,System.Type,System.Object)">
            <summary>
            清除实体的主键值，将自增、Guid类型的主键值清除
            </summary>
            <param name="orm"></param>
            <param name="entityType"></param>
            <param name="entity"></param>
        </member>
        <member name="M:FreeSql.Extensions.EntityUtil.EntityUtilExtensions.CompareEntityValueReturnColumns(IFreeSql,System.Type,System.Object,System.Object,System.Boolean)">
            <summary>
            对比两个实体值，返回相同/或不相同的列名
            </summary>
            <param name="orm"></param>
            <param name="entityType"></param>
            <param name="entity1"></param>
            <param name="entity2"></param>
            <param name="isEqual"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Extensions.EntityUtil.EntityUtilExtensions.SetEntityIncrByWithPropertyName(IFreeSql,System.Type,System.Object,System.String,System.Int32)">
            <summary>
            设置实体中某属性的数值增加指定的值
            </summary>
            <param name="orm"></param>
            <param name="entityType"></param>
            <param name="entity"></param>
            <param name="propertyName"></param>
            <param name="incrBy"></param>
        </member>
        <member name="M:FreeSql.Extensions.EntityUtil.EntityUtilExtensions.SetEntityValueWithPropertyName(IFreeSql,System.Type,System.Object,System.String,System.Object)">
            <summary>
            设置实体中某属性的值
            </summary>
            <param name="orm"></param>
            <param name="entityType"></param>
            <param name="entity"></param>
            <param name="propertyName"></param>
            <param name="value"></param>
        </member>
        <member name="M:FreeSql.Extensions.EntityUtil.EntityUtilExtensions.AppendEntityUpdateSetWithColumn``1(FreeSql.IUpdate{``0},System.Type,System.Linq.Expressions.LambdaExpression)">
            <summary>
            缓存执行 IUpdate.Set
            </summary>
            <typeparam name="TEntity"></typeparam>
            <param name="update"></param>
            <param name="columnType"></param>
            <param name="setExp"></param>
        </member>
        <member name="T:FreeSql.SqlExt">
            <summary>
            SqlExt 是利用自定表达式函数解析功能，解析默认常用的SQL函数，欢迎 PR
            </summary>
        </member>
        <member name="M:FreeSql.SqlExt.AggregateCount">
            <summary>
            count(1)
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.AggregateCount``1(``0)">
            <summary>
            count(column)<para></para>
            或者<para></para>
            sum(case when column then 1 else 0 end)
            </summary>
            <typeparam name="T3"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.AggregateSum``1(``0)">
            <summary>
            sum(column)
            </summary>
            <typeparam name="T3"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.AggregateAvg``1(``0)">
            <summary>
            avg(column)
            </summary>
            <typeparam name="T3"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.AggregateMax``1(``0)">
            <summary>
            max(column)
            </summary>
            <typeparam name="T3"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.AggregateMin``1(``0)">
            <summary>
            min(column)
            </summary>
            <typeparam name="T3"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.Rank">
            <summary>
            rank() over(order by ...)
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.DenseRank">
            <summary>
            dense_rank() over(order by ...)
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.Count(System.Object)">
            <summary>
            count() over(order by ...)
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.Sum(System.Object)">
            <summary>
            sum(..) over(order by ...)
            </summary>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.Avg(System.Object)">
            <summary>
            avg(..) over(order by ...)
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.Max``1(``0)">
            <summary>
            max(..) over(order by ...)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.Min``1(``0)">
            <summary>
            min(..) over(order by ...)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.RowNumber">
            <summary>
            SqlServer row_number() over(order by ...)
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.IsNull``1(``0,``0)">
            <summary>
            isnull、ifnull、coalesce、nvl
            </summary>
            <typeparam name="TValue"></typeparam>
            <param name="value"></param>
            <param name="defaultValue"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.DistinctCount``1(``0)">
            <summary>
            count(distinct name)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.InternalRawSql(System.String)">
            <summary>
            注意：使用者自己承担【注入风险】
            </summary>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.GreaterThan``1(``0,``0)">
            <summary>
            大于 &gt;
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.GreaterThanOrEqual``1(``0,``0)">
            <summary>
            大于或等于 &gt;=
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.LessThan``1(``0,``0)">
            <summary>
            小于 &lt;
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.LessThanOrEqual``1(``0,``0)">
            <summary>
            小于或等于 &lt;=
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.EqualIsNull``1(``0)">
            <summary>
            value1  IS  NULL
            </summary>
            <typeparam name="TValue"></typeparam>
            <param name="value1"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.DateDiff(System.String,System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
            计算两个日期之间的差异
            时间2 - 时间1
            </summary>
            <param name="datePart"></param>
            <param name="dateTimeOffset1"></param>
            <param name="dateTimeOffset2"></param>
        </member>
        <member name="M:FreeSql.SqlExt.DateDiff(System.String,System.DateTime,System.DateTime)">
            <summary>
            计算两个日期之间的差异
            时间2 - 时间1
            </summary>
            <param name="datePart"></param>
            <param name="dateTime1">时间1</param>
            <param name="dateTime2">时间2</param>
        </member>
        <member name="M:FreeSql.SqlExt.Case">
            <summary>
            case when .. then .. end
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.CaseDict``2(``0,System.Collections.Generic.Dictionary{``0,``1})">
            <summary>
            case when .. then .. end
            </summary>
            <typeparam name="TInput"></typeparam>
            <typeparam name="TOutput"></typeparam>
            <param name="input"></param>
            <param name="dict"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.GroupConcat(System.Object)">
            <summary>
            MySql group_concat(distinct .. order by .. separator ..)
            </summary>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.FindInSet``1(``0,System.String)">
            <summary>
            MySql find_in_set(str, strlist)
            </summary>
            <typeparam name="TValue"></typeparam>
            <param name="str"></param>
            <param name="strlist"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.SqlExt.StringAgg(System.Object,System.Object)">
            <summary>
            PostgreSQL string_agg(.., ..)
            </summary>
            <param name="column"></param>
            <param name="delimiter"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseConnectionString(FreeSql.DataType,System.String,System.Type)">
            <summary>
            使用连接串（推荐）
            </summary>
            <param name="dataType">数据库类型</param>
            <param name="connectionString">数据库连接串</param>
            <param name="providerType">提供者的类型，一般不需要指定，如果一直提示“缺少 FreeSql 数据库实现包：FreeSql.Provider.MySql.dll，可前往 nuget 下载”的错误，说明反射获取不到类型，此时该参数可排上用场<para></para>例如：typeof(FreeSql.SqlServer.SqlServerProvider&lt;&gt;)</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseCustomTableEntityCacheFactory(System.Func{System.Collections.Concurrent.ConcurrentDictionary{FreeSql.DataType,System.Collections.Concurrent.ConcurrentDictionary{System.Type,FreeSql.Internal.Model.TableInfo}}})">
            <summary>
            用于指定自定义实现TableEntiy 的缓存集合
            解决多实例下相同类型映射到不同表的问题
            </summary>
            <param name="factory"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseAdoConnectionPool(System.Boolean)">
            <summary>
            使用原始连接池（ado.net、odbc、oledb）<para></para>
            默认：false<para></para>
            UseConnectionString 默认使用 FreeSql 连接池，有以下特点：<para></para>
            - 状态不可用，断熔机制直到后台检测恢复<para></para>
            - 读写分离，从库不可用，会切换其他可用从库<para></para>
            - 监测连接池使用情况，fsql.Ado.Statistics<para></para>
            有部分使用者不喜欢【断熔机制】，可使用此设置
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseSlave(System.String[])">
            <summary>
            使用从数据库，支持多个
            </summary>
            <param name="slaveConnectionString">从数据库连接串</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseConnectionFactory(FreeSql.DataType,System.Func{System.Data.Common.DbConnection},System.Type)">
            <summary>
            使用自定义数据库连接对象（放弃内置对象连接池技术）
            </summary>
            <param name="dataType">数据库类型</param>
            <param name="connectionFactory">数据库连接对象创建器</param>
            <param name="providerType">提供者的类型，一般不需要指定，如果一直提示“缺少 FreeSql 数据库实现包：FreeSql.Provider.MySql.dll，可前往 nuget 下载”的错误，说明反射获取不到类型，此时该参数可排上用场<para></para>例如：typeof(FreeSql.SqlServer.SqlServerProvider&lt;&gt;)</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseAutoSyncStructure(System.Boolean)">
            <summary>
            【开发环境必备】自动同步实体结构到数据库，程序运行中检查实体表是否存在，然后创建或修改<para></para>
            注意：生产环境中谨慎使用
            </summary>
            <param name="value">true:运行时检查自动同步结构, false:不同步结构(默认)</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseConfigEntityFromDbFirst(System.Boolean)">
            <summary>
            将数据库的主键、自增、索引设置导入，适用 DbFirst 模式，无须在实体类型上设置 [Column(IsPrimary)] 或者 ConfigEntity。此功能目前可用于 mysql/sqlserver/postgresql/oracle。<para></para>
            本功能会影响 IFreeSql 首次访问的速度。<para></para>
            若使用 CodeFirst 创建索引后，又直接在数据库上建了索引，若无本功能下一次 CodeFirst 迁移时数据库上创建的索引将被删除
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseNoneCommandParameter(System.Boolean)">
            <summary>
            不使用命令参数化执行，针对 Insert/Update，也可临时使用 IInsert/IUpdate.NoneParameter() 
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseGenerateCommandParameterWithLambda(System.Boolean)">
            <summary>
            是否生成命令参数化执行，针对 lambda 表达式解析<para></para>
            注意：常量不会参数化，变量才会做参数化<para></para>
            var id = 100;
            fsql.Select&lt;T&gt;().Where(a => a.id == id) 会参数化<para></para>
            fsql.Select&lt;T&gt;().Where(a => a.id == 100) 不会参数化
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseLazyLoading(System.Boolean)">
            <summary>
            延时加载导航属性对象，导航属性需要声明 virtual
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseMonitorCommand(System.Action{System.Data.Common.DbCommand},System.Action{System.Data.Common.DbCommand,System.String})">
            <summary>
            监视数据库命令对象
            </summary>
            <param name="executing">执行前</param>
            <param name="executed">执行后，可监视执行性能</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseNameConvert(FreeSql.Internal.NameConvertType)">
            <summary>
            实体类名 -> 数据库表名，命名转换（类名、属性名都生效）<para></para>
            优先级小于 [Column(Name = "xxx")]
            </summary>
            <param name="convertType"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseQuoteSqlName(System.Boolean)">
            <summary>
            SQL名称是否使用 [] `` ""<para></para>
            true: SELECT .. FROM [table]<para></para>
            false: SELECT .. FROM table
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseMappingPriority(FreeSql.Internal.MappingPriorityType,FreeSql.Internal.MappingPriorityType,FreeSql.Internal.MappingPriorityType)">
            <summary>
            指定映射优先级（从小到大）<para></para>
            例如表名：实体类名 &lt; Aop &lt; FluentApi &lt; Attribute &lt; AsTable<para></para>
            事件 Aop -------> fsql.Aop.ConfigEntity/fsql.Aop.ConfigEntityProperty<para></para>
            方法 FluentApi -> fsql.CodeFirst.ConfigEntity/fsql.CodeFirst.Entity<para></para>
            特性 Attribute -> [Table(Name = xxx, ...)]<para></para>
            -----------------------------------------------------------------------------<para></para>
            默认规则：关于映射优先级，Attribute 可以更直观排查问题，即使任何地方使用 FluentApi/Aop 设置 TableName 都不生效。<para></para>
            调整规则：UseMappingPriority(Attribute, FluentApi, Aop) <para></para>
            实体类名 &lt; Attribute &lt; FluentApi &lt; Aop &lt; AsTable
            </summary>
            <param name="mappingType1"></param>
            <param name="mappingType2"></param>
            <param name="mappingType3"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:FreeSql.FreeSqlBuilder.UseExitAutoDisposePool(System.Boolean)">
            <summary>
            监听 AppDomain.CurrentDomain.ProcessExit/Console.CancelKeyPress 事件自动释放连接池<para></para>
            默认值: true
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.WithTransaction(System.Data.Common.DbTransaction)">
            <summary>
            指定事务对象
            </summary>
            <param name="transaction"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.WithConnection(System.Data.Common.DbConnection)">
            <summary>
            指定事务对象
            </summary>
            <param name="connection"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.CommandTimeout(System.Int32)">
            <summary>
            命令超时设置(秒)
            </summary>
            <param name="timeout"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.Where(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            lambda表达式条件，仅支持实体基础成员（不包含导航对象）<para></para>
            若想使用导航对象，请使用 ISelect.ToDelete() 方法
            </summary>
            <param name="exp">lambda表达式条件</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.WhereIf(System.Boolean,System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            lambda表达式条件，仅支持实体基础成员（不包含导航对象）<para></para>
            若想使用导航对象，请使用 ISelect.ToUpdate() 方法
            </summary>
            <param name="condition">true 时生效</param>
            <param name="exp">lambda表达式条件</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.Where(System.String,System.Object)">
            <summary>
            原生sql语法条件，Where("id = @id", new { id = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">sql语法条件</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.WhereIf(System.Boolean,System.String,System.Object)">
            <summary>
            原生sql语法条件，Where("id = @id", new { id = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="condition">true 时生效</param>
            <param name="sql">sql语法条件</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.Where(`0)">
            <summary>
            传入实体，将主键作为条件
            </summary>
            <param name="item">实体</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.Where(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            传入实体集合，将主键作为条件
            </summary>
            <param name="items">实体集合</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.WhereDynamic(System.Object,System.Boolean)">
            <summary>
            传入动态条件，如：主键值 | new[]{主键值1,主键值2} | TEntity1 | new[]{TEntity1,TEntity2} | new{id=1}
            </summary>
            <param name="dywhere">主键值、主键值集合、实体、实体集合、匿名对象、匿名对象集合</param>
            <param name="not">是否标识为NOT</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.WhereDynamicFilter(FreeSql.Internal.Model.DynamicFilterInfo)">
            <summary>
            动态过滤条件
            </summary>
            <param name="filter"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.DisableGlobalFilter(System.String[])">
            <summary>
            禁用全局过滤功能，不传参数时将禁用所有
            </summary>
            <param name="name">零个或多个过滤器名字</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.AsTable(System.Func{System.String,System.String})">
            <summary>
            设置表名规则，可用于分库/分表，参数1：默认表名；返回值：新表名；
            </summary>
            <param name="tableRule"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.AsTable(System.String)">
            <summary>
            设置表名
            </summary>
            <param name="tableName"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.AsType(System.Type)">
            <summary>
            动态Type，在使用 Delete&lt;object&gt; 后使用本方法，指定实体类型
            </summary>
            <param name="entityType"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.ToSql">
            <summary>
            返回即将执行的SQL语句
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.ExecuteAffrows">
            <summary>
            执行SQL语句，返回影响的行数
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.ExecuteDeleted">
            <summary>
            执行SQL语句，返回被删除的记录<para></para>
            注意：此方法只有 Postgresql/SqlServer/Maridb/Firebird/人大金仓 有效果
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDelete`1.ExecuteDeletedAsync(System.Threading.CancellationToken)">
            <summary>
            执行SQL语句，返回被删除的记录<para></para>
            注意：此方法只有 Postgresql/SqlServer/Maridb/Firebird/人大金仓 有效果
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.WithTransaction(System.Data.Common.DbTransaction)">
            <summary>
            指定事务对象
            </summary>
            <param name="transaction"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.WithConnection(System.Data.Common.DbConnection)">
            <summary>
            指定事务对象
            </summary>
            <param name="connection"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.CommandTimeout(System.Int32)">
            <summary>
            命令超时设置(秒)
            </summary>
            <param name="timeout"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.AppendData(`0)">
            <summary>
            追加准备插入的实体
            </summary>
            <param name="source">实体</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.AppendData(`0[])">
            <summary>
            追加准备插入的实体
            </summary>
            <param name="source">实体</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.AppendData(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            追加准备插入的实体集合
            </summary>
            <param name="source">实体集合</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.InsertColumns(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            只插入的列，InsertColumns(a => a.Name) | InsertColumns(a => new{a.Name,a.Time}) | InsertColumns(a => new[]{"name","time"})
            </summary>
            <param name="columns">lambda选择列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.InsertColumns(System.String[])">
            <summary>
            只插入的列
            </summary>
            <param name="columns">属性名，或者字段名</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.IgnoreColumns(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            忽略的列，IgnoreColumns(a => a.Name) | IgnoreColumns(a => new{a.Name,a.Time}) | IgnoreColumns(a => new[]{"name","time"})
            </summary>
            <param name="columns">lambda选择列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.IgnoreColumns(System.String[])">
            <summary>
            忽略的列
            </summary>
            <param name="columns">属性名，或者字段名</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.IgnoreInsertValueSql(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            忽略 InsertValueSql 设置，将使用实体对象的值插入<para></para>
            IgnoreInsertValueSql(a => a.Name) | IgnoreInsertValueSql(a => new{a.Name,a.Time}) | IgnoreInsertValueSql(a => new[]{"name","time"})
            </summary>
            <param name="columns">属性名，或者字段名</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.InsertIdentity">
            <summary>
            指定可插入自增字段
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.NoneParameter(System.Boolean)">
            <summary>
            不使用参数化，可通过 IFreeSql.CodeFirst.IsNotCommandParameter 全局性设置
            </summary>
            <param name="isNotCommandParameter">是否不使用参数化</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.BatchOptions(System.Int32,System.Int32,System.Boolean)">
            <summary>
            批量执行选项设置，一般不需要使用该方法<para></para>
            各数据库 values, parameters 限制不一样，默认设置：<para></para>
            MySql 5000 3000<para></para>
            PostgreSQL 5000 3000<para></para>
            SqlServer 1000 2100<para></para>
            Oracle 500 999<para></para>
            Sqlite 5000 999<para></para>
            若没有事务传入，内部(默认)会自动开启新事务，保证拆包执行的完整性。
            </summary>
            <param name="valuesLimit">指定根据 values 上限数量拆分执行</param>
            <param name="parameterLimit">指定根据 parameters 上限数量拆分执行</param>
            <param name="autoTransaction">是否自动开启事务</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.BatchProgress(System.Action{FreeSql.Internal.Model.BatchProgressStatus{`0}})">
            <summary>
            批量执行时，分批次执行的进度状态
            </summary>
            <param name="callback">批量执行时的回调委托</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.AsTable(System.Func{System.String,System.String})">
            <summary>
            设置表名规则，可用于分库/分表，参数1：默认表名；返回值：新表名；
            </summary>
            <param name="tableRule"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.AsTable(System.String)">
            <summary>
            设置表名
            </summary>
            <param name="tableName"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.AsType(System.Type)">
            <summary>
            动态Type，在使用 Insert&lt;object&gt; 后使用本方法，指定实体类型
            </summary>
            <param name="entityType"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.ToSql">
            <summary>
            返回即将执行的SQL语句
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.ExecuteAffrows">
            <summary>
            执行SQL语句，返回影响的行数
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.ExecuteIdentity">
            <summary>
            执行SQL语句，返回自增值<para></para>
            注意：请检查实体类是否标记了 [Column(IsIdentity = true)]
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.ExecuteInserted">
            <summary>
            执行SQL语句，返回插入后的记录<para></para>
            注意：此方法只有 Postgresql/SqlServer/Maridb/Firebird/DuckDB/人大金仓 有效果
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.ToDataTable">
            <summary>
            返回 DataTable 以便做 BulkCopy 数据做准备<para></para>
            此方法会处理：<para></para>
            类型、表名、字段名映射<para></para>
            IgnoreColumns、InsertColumns
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsert`1.ExecuteInsertedAsync(System.Threading.CancellationToken)">
            <summary>
            执行SQL语句，返回插入后的记录<para></para>
            注意：此方法只有 Postgresql/SqlServer/Maridb/Firebird/DuckDB/人大金仓 有效果
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.WithTransaction(System.Data.Common.DbTransaction)">
            <summary>
            指定事务对象
            </summary>
            <param name="transaction"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.WithConnection(System.Data.Common.DbConnection)">
            <summary>
            指定事务对象
            </summary>
            <param name="connection"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.CommandTimeout(System.Int32)">
            <summary>
            命令超时设置(秒)
            </summary>
            <param name="timeout"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.SetSource(`0)">
            <summary>
            添加或更新，设置实体
            </summary>
            <param name="source">实体</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.SetSource(`0,System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            添加或更新，设置实体
            </summary>
            <param name="source">实体</param>
            <param name="tempPrimarys">
            根据临时主键插入或更新，a => a.Name | a => new{a.Name,a.Time} | a => new[]{"name","time"}<para></para>
            注意：不处理自增，因某些数据库依赖主键或唯一键，所以指定临时主键仅对 SqlServer/PostgreSQL/Firebird/达梦/南大通用/金仓/神通 有效
            </param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.SetSource(System.Collections.Generic.IEnumerable{`0},System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            添加或更新，设置实体集合
            </summary>
            <param name="source">实体集合</param>
            <param name="tempPrimarys">
            根据临时主键插入或更新，a => a.Name | a => new{a.Name,a.Time} | a => new[]{"name","time"}<para></para>
            注意：不处理自增，因某些数据库依赖主键或唯一键，所以指定临时主键仅对 SqlServer/PostgreSQL/Firebird/达梦/南大通用/金仓/神通 有效
            </param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.SetSource(System.String,System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            添加或更新，设置SQL
            </summary>
            <param name="sql">查询SQL</param>
            <param name="tempPrimarys">
            根据临时主键插入或更新，a => a.Name | a => new{a.Name,a.Time} | a => new[]{"name","time"}<para></para>
            注意：不处理自增，因某些数据库依赖主键或唯一键，所以指定临时主键仅对 SqlServer/PostgreSQL/Firebird/达梦/南大通用/金仓/神通 有效
            </param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.IfExistsDoNothing">
            <summary>
            当记录存在时，什么都不做<para></para>
            换句话：只有记录不存在时才插入
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.UpdateColumns(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            当记录存在时，指定只更新的字段，UpdateColumns(a => a.Name) | UpdateColumns(a => new{a.Name,a.Time}) | UpdateColumns(a => new[]{"name","time"})
            </summary>
            <param name="columns">lambda选择列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.UpdateColumns(System.String[])">
            <summary>
            当记录存在时，指定只更新的字段
            </summary>
            <param name="columns">属性名，或者字段名</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.UpdateSet``1(System.Linq.Expressions.Expression{System.Func{`0,`0,``0}})">
            <summary>
            设置列的联表值，格式：<para></para>
            UpdateSet((a, b) => a.Clicks == b.xxx)<para></para>
            UpdateSet((a, b) => a.Clicks == a.Clicks + 1)
            </summary>
            <typeparam name="TMember"></typeparam>
            <param name="exp"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.BatchOptions(System.Int32,System.Boolean)">
            <summary>
            批量执行选项设置，一般不需要使用该方法<para></para>
            各数据库 rows 限制不一样，默认设置：200<para></para>
            若没有事务传入，内部(默认)会自动开启新事务，保证拆包执行的完整性。
            </summary>
            <param name="rowsLimit">指定根据 rows 上限数量拆分执行</param>
            <param name="autoTransaction">是否自动开启事务</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.AsTable(System.Func{System.String,System.String})">
            <summary>
            设置表名规则，可用于分库/分表，参数1：默认表名；返回值：新表名；
            </summary>
            <param name="tableRule"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.AsTable(System.String)">
            <summary>
            设置表名
            </summary>
            <param name="tableName"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.AsType(System.Type)">
            <summary>
            动态Type，在使用 Update&lt;object&gt; 后使用本方法，指定实体类型
            </summary>
            <param name="entityType"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.ToSql">
            <summary>
            返回即将执行的SQL语句
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IInsertOrUpdate`1.ExecuteAffrows">
            <summary>
            执行SQL语句，返回影响的行数
            </summary>
            <returns></returns>
        </member>
        <member name="F:FreeSql.FieldAliasOptions.AsIndex">
            <summary>
            自动产生 as1, as2, as3 .... 字段别名<para></para>
            这种方法可以最大程度防止多表，存在相同字段的问题
            </summary>
        </member>
        <member name="F:FreeSql.FieldAliasOptions.AsProperty">
            <summary>
            使用属性名作为字段别名
            </summary>
        </member>
        <member name="F:FreeSql.FieldAliasOptions.AsEmpty">
            <summary>
            没有字段别名
            </summary>
        </member>
        <member name="M:FreeSql.ISelect0`2.Cancel(System.Func{System.Boolean})">
            <summary>
            控制取消本次查询<para></para>
            * 不会产生额外的异常<para></para>
            * 取消成功，则不执行 SQL 命令<para></para>
            * 取消成功，直接返回没有记录时候的返回值<para></para>
            * 取消成功，如 List&lt;T&gt; 返回 0 元素列表，不是 null，仍然是旧机制<para></para>
            </summary>
            <param name="cancel">返回 true，则不会执行 SQL 命令</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.WithTransaction(System.Data.Common.DbTransaction)">
            <summary>
            指定事务对象
            </summary>
            <param name="transaction"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.WithConnection(System.Data.Common.DbConnection)">
            <summary>
            指定连接对象
            </summary>
            <param name="connection"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.WithParameters(System.Collections.Generic.List{System.Data.Common.DbParameter})">
            <summary>
            使用自定义参数化，UnionALL 或者 ToSql 可能有需要
            </summary>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.CommandTimeout(System.Int32)">
            <summary>
            命令超时设置(秒)
            </summary>
            <param name="timeout"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.TrackToList(System.Action{System.Object})">
            <summary>
            审核或跟踪 ToList 即将返回的数据
            </summary>
            <param name="action"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.ToDataTable(System.String)">
            <summary>
            执行SQL查询，返回 DataTable
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.ToDataTableByPropertyName(System.String[])">
            <summary>
            执行SQL查询，返回 properties 指定的实体类属性，并以 DataTable 接收
            </summary>
            <param name="properties">属性名：Name<para></para>导航属性：Parent.Name<para></para>多表：b.Name</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.ToDictionary``1(System.Func{`1,``0})">
            <summary>
            以字典的形式返回查询结果<para></para>
            注意：字典的特点会导致返回的数据无序
            </summary>
            <typeparam name="TKey"></typeparam>
            <param name="keySelector"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.ToList">
            <summary>
            执行SQL查询，返回 T1 实体所有字段的记录，记录不存在时返回 Count 为 0 的列表<para></para>
            注意：<para></para>
            1、ToList(a => a) 可以返回 a 所有实体<para></para>
            2、ToList(a => new { a }) 这样也可以<para></para>
            3、ToList((a, b, c) => new { a, b, c }) 这样也可以<para></para>
            4、abc 怎么来的？请试试 fsql.Select&lt;T1, T2, T3&gt;()
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.ToList(System.Boolean)">
            <summary>
            执行SQL查询，返回 T1 实体、以及 LeftJoin/InnerJoin/RightJoin 对象
            </summary>
            <param name="includeNestedMembers">false: 返回 2级 LeftJoin/InnerJoin/RightJoin 对象；true: 返回所有 LeftJoin/InnerJoin/RightJoin 的导航数据</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.ToChunk(System.Int32,System.Action{FreeSql.Internal.Model.FetchCallbackArgs{System.Collections.Generic.List{`1}}})">
            <summary>
            执行SQL查询，分块返回数据，可减少内存开销。比如读取10万条数据，每次返回100条处理。
            </summary>
            <param name="size">数据块的大小</param>
            <param name="done">处理数据块</param>
        </member>
        <member name="M:FreeSql.ISelect0`2.ToList``1(System.String)">
            <summary>
            执行SQL查询，返回 field 指定字段的记录，并以元组或基础类型(int,string,long)接收，记录不存在时返回 Count 为 0 的列表
            </summary>
            <typeparam name="TTuple"></typeparam>
            <param name="field"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.ToOne">
            <summary>
            执行SQL查询，返回 T1 实体所有字段的第一条记录，记录不存在时返回 null
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.First">
            <summary>
            执行SQL查询，返回 T1 实体所有字段的第一条记录，记录不存在时返回 null
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.ToDelete">
            <summary>
            将查询转为删除对象，以便支持导航对象或其他查询功能删除数据，如下：<para></para>
            fsql.Select&lt;T1&gt;().Where(a => a.Options.xxx == 1).ToDelete().ExecuteAffrows()<para></para>
            注意：此方法不是将数据查询到内存循环删除，上面的代码产生如下 SQL 执行：<para></para>
            DELETE FROM `T1` WHERE id in (select a.id from T1 a left join Options b on b.t1id = a.id where b.xxx = 1)<para></para>
            复杂删除使用该方案的好处：<para></para>
            1、删除前可预览测试数据，防止错误删除操作；<para></para>
            2、支持更加复杂的删除操作（IDelete 默认只支持简单的操作）；
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.ToUpdate">
            <summary>
            将查询转为更新对象，以便支持导航对象或其他查询功能更新数据，如下：<para></para>
            fsql.Select&lt;T1&gt;().Where(a => a.Options.xxx == 1).ToUpdate().Set(a => a.Title, "111").ExecuteAffrows()<para></para>
            注意：此方法不是将数据查询到内存循环更新，上面的代码产生如下 SQL 执行：<para></para>
            UPDATE `T1` SET Title = '111' WHERE id in (select a.id from T1 a left join Options b on b.t1id = a.id where b.xxx = 1)<para></para>
            复杂更新使用该方案的好处：<para></para>
            1、更新前可预览测试数据，防止错误更新操作；<para></para>
            2、支持更加复杂的更新操作（IUpdate 默认只支持简单的操作）；
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.AsTable(System.Func{System.Type,System.String,System.String})">
            <summary>
            设置表名规则，可用于分库/分表，参数1：实体类型；参数2：默认表名；返回值：新表名； <para></para>
            设置多次，可查询分表后的多个子表记录，以 UNION ALL 形式执行。 <para></para>
            如：select.AsTable((type, oldname) => "table_1").AsTable((type, oldname) => "table_2").AsTable((type, oldname) => "table_3").ToSql(a => a.Id); <para></para>
            select * from (SELECT a."Id" as1 FROM "table_1" a) ftb <para></para>
            UNION ALL select * from (SELECT a."Id" as1 FROM "table_2" a) ftb <para></para>
            UNION ALL select * from (SELECT a."Id" as1 FROM "table_3" a) ftb <para></para>
            还可以这样：select.AsTable((a, b) => "(select * from tb_topic where clicks > 10)").Page(1, 10).ToList()
            </summary>
            <param name="tableRule"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.AsAlias(System.Func{System.Type,System.String,System.String})">
            <summary>
            设置别名规则，可用于拦截表别名，实现类似 sqlserver 的 with(nolock) 需求<para></para>
            如：select.AsAlias((_, old) => $"{old} with(lock)")
            </summary>
            <param name="aliasRule"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.AsType(System.Type)">
            <summary>
            动态Type，在使用 Select&lt;object&gt; 后使用本方法，指定实体类型
            </summary>
            <param name="entityType"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.ToSql(System.String)">
            <summary>
            返回即将执行的SQL语句
            </summary>
            <param name="field">指定字段</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.Any">
            <summary>
            执行SQL查询，是否有记录
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.Count">
            <summary>
            查询的记录数量
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.Count(System.Int64@)">
            <summary>
            查询的记录数量，以参数out形式返回
            </summary>
            <param name="count">返回的变量</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.Master">
            <summary>
            指定从主库查询（默认查询从库）
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.LeftJoin(System.Linq.Expressions.Expression{System.Func{`1,System.Boolean}})">
            <summary>
            左联查询，使用导航属性自动生成SQL
            </summary>
            <param name="exp">表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.InnerJoin(System.Linq.Expressions.Expression{System.Func{`1,System.Boolean}})">
            <summary>
            联接查询，使用导航属性自动生成SQL
            </summary>
            <param name="exp">表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.RightJoin(System.Linq.Expressions.Expression{System.Func{`1,System.Boolean}})">
            <summary>
            右联查询，使用导航属性自动生成SQL
            </summary>
            <param name="exp">表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.LeftJoin``1(System.Linq.Expressions.Expression{System.Func{`1,``0,System.Boolean}})">
            <summary>
            左联查询，指定关联的实体类型
            </summary>
            <typeparam name="T2">关联的实体类型</typeparam>
            <param name="exp">表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.InnerJoin``1(System.Linq.Expressions.Expression{System.Func{`1,``0,System.Boolean}})">
            <summary>
            联接查询，指定关联的实体类型
            </summary>
            <typeparam name="T2">关联的实体类型</typeparam>
            <param name="exp">表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.RightJoin``1(System.Linq.Expressions.Expression{System.Func{`1,``0,System.Boolean}})">
            <summary>
            右联查询，指定关联的实体类型
            </summary>
            <typeparam name="T2">关联的实体类型</typeparam>
            <param name="exp">表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.LeftJoin(System.String,System.Object)">
            <summary>
            左联查询，使用原生sql语法，LeftJoin("type b on b.id = a.id and b.clicks > @clicks", new { clicks = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">sql语法条件</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.InnerJoin(System.String,System.Object)">
            <summary>
            联接查询，使用原生sql语法，InnerJoin("type b on b.id = a.id and b.clicks > @clicks", new { clicks = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">sql语法条件</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.RightJoin(System.String,System.Object)">
            <summary>
            右联查询，使用原生sql语法，RightJoin("type b on b.id = a.id and b.clicks > @clicks", new { clicks = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">sql语法条件</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.RawJoin(System.String)">
            <summary>
            在 JOIN 位置插入 SQL 内容<para></para>
            如：.RawJoin("OUTER APPLY ( select id from t2 ) b")
            </summary>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.Where(System.String,System.Object)">
            <summary>
            原生sql语法条件，Where("id = @id", new { id = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">sql语法条件</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.WhereIf(System.Boolean,System.String,System.Object)">
            <summary>
            原生sql语法条件，WhereIf(true, "id = @id", new { id = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="condition">true 时生效</param>
            <param name="sql">sql语法条件</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.WhereDynamicFilter(FreeSql.Internal.Model.DynamicFilterInfo)">
            <summary>
            动态过滤条件
            </summary>
            <param name="filter"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.DisableGlobalFilter(System.String[])">
            <summary>
            禁用全局过滤功能，不传参数时将禁用所有
            </summary>
            <param name="name">零个或多个过滤器名字</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.ForUpdate(System.Boolean,System.Boolean)">
            <summary>
            排他更新锁<para></para>
            注意：务必在开启事务后使用该功能<para></para>
            MySql: for update<para></para>
            SqlServer: With(UpdLock, RowLock, NoWait)<para></para>
            PostgreSQL: for update nowait<para></para>
            Oracle: for update nowait<para></para>
            Sqlite: 无效果<para></para>
            达梦: for update nowait<para></para>
            人大金仓: for update nowait<para></para>
            神通: for update
            </summary>
            <param name="nowait">noawait</param>
            <param name="skipLocked">skip locked</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.GroupBy(System.String,System.Object)">
            <summary>
            按原生sql语法分组，GroupBy("concat(name, @cc)", new { cc = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">sql语法</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.Having(System.String,System.Object)">
            <summary>
            按原生sql语法聚合条件过滤，Having("count(name) = @cc", new { cc = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">sql语法条件</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.OrderBy(System.String,System.Object)">
            <summary>
            按原生sql语法排序，OrderBy("count(name) + @cc desc", new { cc = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">sql语法</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.OrderBy(System.Boolean,System.String,System.Object)">
            <summary>
            按原生sql语法排序，OrderBy(true, "count(name) + @cc desc", new { cc = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="condition">true 时生效</param>
            <param name="sql">sql语法</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.OrderByPropertyName(System.String,System.Boolean)">
            <summary>
            按属性名字符串排序（支持导航属性）<para></para>
            属性名：Name<para></para>导航属性：Parent.Name<para></para>多表：b.Name
            </summary>
            <param name="property">属性名：Name<para></para>导航属性：Parent.Name<para></para>多表：b.Name</param>
            <param name="isAscending">顺序 | 倒序</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.OrderByPropertyNameIf(System.Boolean,System.String,System.Boolean)">
            <summary>
            按属性名字符串排序（支持导航属性）<para></para>
            属性名：Name<para></para>导航属性：Parent.Name<para></para>多表：b.Name
            </summary>
            <param name="condition">true 时生效</param>
            <param name="property">属性名：Name<para></para>导航属性：Parent.Name<para></para>多表：b.Name</param>
            <param name="isAscending">顺序 | 倒序</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.Skip(System.Int32)">
            <summary>
            查询向后偏移行数
            </summary>
            <param name="offset"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.Offset(System.Int32)">
            <summary>
            查询向后偏移行数
            </summary>
            <param name="offset">行数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.Limit(System.Int32)">
            <summary>
            查询多少条数据
            </summary>
            <param name="limit"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.Take(System.Int32)">
            <summary>
            查询多少条数据
            </summary>
            <param name="limit"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.Page(System.Int32,System.Int32)">
            <summary>
            分页
            </summary>
            <param name="pageNumber">第几页</param>
            <param name="pageSize">每页多少</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.Page(FreeSql.Internal.Model.BasePagingInfo)">
            <summary>
            分页
            </summary>
            <param name="pagingInfo">分页信息</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect0`2.Distinct">
            <summary>
            查询数据前，去重
            <para>
            .Distinct().ToList(x => x.GroupName) 对指定字段去重
            </para>
            <para>
            .Distinct().ToList() 对整个查询去重
            </para>
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.Any(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            执行SQL查询，是否有记录
            </summary>
            <param name="exp">lambda表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.InsertInto``1(System.String,System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            将查询转换为 INSERT INTO tableName SELECT ... FROM t 执行插入
            </summary>
            <typeparam name="TTargetEntity"></typeparam>
            <param name="tableName">指定插入的表名，若为 null 则使用 TTargetEntity 实体表名</param>
            <param name="select">选择列</param>
            <returns>返回影响的行数</returns>
        </member>
        <member name="M:FreeSql.ISelect`1.ToDataTable``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            执行SQL查询，返回 DataTable
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.ToList``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            执行SQL查询，返回指定字段的记录，记录不存在时返回 Count 为 0 的列表
            </summary>
            <typeparam name="TReturn">返回类型</typeparam>
            <param name="select">选择列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.ToList``1">
            <summary>
            执行SQL查询，返回 TDto 映射的字段，记录不存在时返回 Count 为 0 的列表
            </summary>
            <typeparam name="TDto"></typeparam>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.ToChunk``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Int32,System.Action{FreeSql.Internal.Model.FetchCallbackArgs{System.Collections.Generic.List{``0}}})">
            <summary>
            执行SQL查询，分块返回数据，可减少内存开销。比如读取10万条数据，每次返回100条处理。
            </summary>
            <typeparam name="TReturn">返回类型</typeparam>
            <param name="select">选择列</param>
            <param name="size">数据块的大小</param>
            <param name="done">处理数据块</param>
        </member>
        <member name="M:FreeSql.ISelect`1.ToOne``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            执行SQL查询，返回指定字段的记录的第一条记录，记录不存在时返回 TReturn 默认值
            </summary>
            <typeparam name="TReturn">返回类型</typeparam>
            <param name="select">选择列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.ToOne``1">
            <summary>
            执行SQL查询，返回 TDto 映射的字段，记录不存在时返回 Dto 默认值
            </summary>
            <typeparam name="TDto"></typeparam>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.First``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            执行SQL查询，返回指定字段的记录的第一条记录，记录不存在时返回 TReturn 默认值
            </summary>
            <typeparam name="TReturn">返回类型</typeparam>
            <param name="select">选择列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.First``1">
            <summary>
            执行SQL查询，返回 TDto 映射的字段，记录不存在时返回 Dto 默认值
            </summary>
            <typeparam name="TDto"></typeparam>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.ToSql``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},FreeSql.FieldAliasOptions)">
            <summary>
            返回即将执行的SQL语句
            </summary>
            <typeparam name="TReturn">返回类型</typeparam>
            <param name="select">选择列</param>
            <param name="fieldAlias">字段别名</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.ToAggregate``1(System.Linq.Expressions.Expression{System.Func{FreeSql.ISelectGroupingAggregate{`0},``0}})">
            <summary>
            执行SQL查询，返回指定字段的聚合结果
            </summary>
            <typeparam name="TReturn"></typeparam>
            <param name="select"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.Aggregate``1(System.Linq.Expressions.Expression{System.Func{FreeSql.ISelectGroupingAggregate{`0},``0}},``0@)">
            <summary>
            执行SQL查询，返回指定字段的聚合结果给 output 参数<para></para>
            fsql.Select&lt;T&gt;()<para></para>
            .Aggregate(a =&gt; new { count = a.Count, sum = a.Sum(a.Key.Price) }, out var agg)<para></para>
            .Page(1, 10).ToList();
            </summary>
            <typeparam name="TReturn"></typeparam>
            <param name="select"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.Sum``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            求和
            </summary>
            <typeparam name="TMember">返回类型</typeparam>
            <param name="column">列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.Min``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            最小值
            </summary>
            <typeparam name="TMember">返回类型</typeparam>
            <param name="column">列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.Max``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            最大值
            </summary>
            <typeparam name="TMember">返回类型</typeparam>
            <param name="column">列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.Avg``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            平均值
            </summary>
            <typeparam name="TMember">返回类型</typeparam>
            <param name="column">列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.As(System.String)">
            <summary>
            指定别名
            </summary>
            <param name="alias">别名</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.From``1(System.Linq.Expressions.Expression{System.Func{FreeSql.ISelectFromExpression{`0},``0,FreeSql.ISelectFromExpression{`0}}})">
            <summary>
            多表查询
            </summary>
            <typeparam name="T2"></typeparam>
            <param name="exp"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.Where(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            查询条件，Where(a => a.Id > 10)，支持导航对象查询，Where(a => a.Author.Email == "<EMAIL>")
            </summary>
            <param name="exp">lambda表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.WhereIf(System.Boolean,System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            查询条件，Where(true, a => a.Id > 10)，支导航对象查询，Where(true, a => a.Author.Email == "<EMAIL>")
            </summary>
            <param name="condition">true 时生效</param>
            <param name="exp">lambda表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.Where``1(System.Linq.Expressions.Expression{System.Func{`0,``0,System.Boolean}})">
            <summary>
            多表条件查询
            </summary>
            <typeparam name="T2"></typeparam>
            <param name="exp">lambda表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.Where``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            多表条件查询
            </summary>
            <typeparam name="T2"></typeparam>
            <param name="exp">lambda表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.Where``2(System.Linq.Expressions.Expression{System.Func{`0,``0,``1,System.Boolean}})">
            <summary>
            多表条件查询
            </summary>
            <typeparam name="T2"></typeparam>
            <typeparam name="T3"></typeparam>
            <param name="exp">lambda表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.Where``3(System.Linq.Expressions.Expression{System.Func{`0,``0,``1,``2,System.Boolean}})">
            <summary>
            多表条件查询
            </summary>
            <typeparam name="T2"></typeparam>
            <typeparam name="T3"></typeparam>
            <typeparam name="T4"></typeparam>
            <param name="exp">lambda表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.Where``4(System.Linq.Expressions.Expression{System.Func{`0,``0,``1,``2,``3,System.Boolean}})">
            <summary>
            多表条件查询
            </summary>
            <typeparam name="T2"></typeparam>
            <typeparam name="T3"></typeparam>
            <typeparam name="T4"></typeparam>
            <typeparam name="T5"></typeparam>
            <param name="exp">lambda表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.WhereDynamic(System.Object,System.Boolean)">
            <summary>
            传入动态条件，如：主键值 | new[]{主键值1,主键值2} | TEntity1 | new[]{TEntity1,TEntity2} | new{id=1}
            </summary>
            <param name="dywhere">主键值、主键值集合、实体、实体集合、匿名对象、匿名对象集合</param>
            <param name="not">是否标识为NOT</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.WhereCascade(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            多表查询时，该方法标记后，表达式条件将对所有表进行附加
            <para></para>
            例如：软删除、租户，每个表都给条件，挺麻烦的
            <para></para>
            fsql.Select&lt;T1&gt;().LeftJoin&lt;T2&gt;(...).Where&lt;T2&gt;((t1, t2 => t1.IsDeleted == false &amp;&amp; t2.IsDeleted == false)
            <para></para>
            修改：fsql.Select&lt;T1&gt;().LeftJoin&lt;T2&gt;(...).WhereCascade(t1 => t1.IsDeleted == false)
            <para></para>
            当其中的实体可附加表达式才会进行，表越多时收益越大
            </summary>
            <param name="exp"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.GroupBy``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            按选择的列分组，GroupBy(a => a.Name) | GroupBy(a => new{a.Name,a.Time})
            </summary>
            <param name="exp"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.GroupBySelf``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            按选择的列分组，GroupBy(a => a.Name) | GroupBy(a => new{a.Name,a.Time})<para></para>
            * GroupBy 返回 ISelectGrouping&lt;T&gt;<para></para>
            * GroupBySelf 返回 ISelect&lt;T&gt;（限制少）
            </summary>
            <typeparam name="TMember"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.OrderBy``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            按列排序，OrderBy(a => a.Time)
            </summary>
            <typeparam name="TMember"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.OrderBy``1(System.Boolean,System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            按列排序，OrderBy(true, a => a.Time)
            </summary>
            <typeparam name="TMember"></typeparam>
            <param name="condition">true 时生效</param>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.OrderByIf``1(System.Boolean,System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean)">
            <summary>
            按列排序，OrderByIf(true, a => a.Time)
            </summary>
            <typeparam name="TMember"></typeparam>
            <param name="condition">true 时生效</param>
            <param name="column"></param>
            <param name="descending">true: DESC, false: ASC</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.OrderByDescending``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            按列倒向排序，OrderByDescending(a => a.Time)
            </summary>
            <param name="column">列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.OrderByDescending``1(System.Boolean,System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            按列倒向排序，OrderByDescending(true, a => a.Time)
            </summary>
            <param name="condition">true 时生效</param>
            <param name="column">列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.Include``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            贪婪加载导航属性，如果查询中已经使用了 a.Parent.Parent 类似表达式，则可以无需此操作
            </summary>
            <typeparam name="TNavigate"></typeparam>
            <param name="navigateSelector">选择一个导航属性</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.IncludeIf``1(System.Boolean,System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            贪婪加载导航属性，如果查询中已经使用了 a.Parent.Parent 类似表达式，则可以无需此操作
            </summary>
            <typeparam name="TNavigate"></typeparam>
            <param name="condition">true 时生效</param>
            <param name="navigateSelector">选择一个导航属性</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.IncludeMany``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Action{FreeSql.ISelect{``0}})">
            <summary>
            贪婪加载集合的导航属性，其实是分两次查询，ToList 后进行了数据重装<para></para>
            文档：https://github.com/dotnetcore/FreeSql/wiki/%E8%B4%AA%E5%A9%AA%E5%8A%A0%E8%BD%BD
            </summary>
            <typeparam name="TNavigate"></typeparam>
            <param name="navigateSelector">选择一个集合的导航属性，如： .IncludeMany(a => a.Tags)<para></para>
            可以 .Where 设置临时的关系映射，如： .IncludeMany(a => a.Tags.Where(tag => tag.TypeId == a.Id))<para></para>
            可以 .Take(5) 每个子集合只取5条，如： .IncludeMany(a => a.Tags.Take(5))<para></para>
            可以 .Select 设置只查询部分字段，如： (a => new TNavigate { Title = a.Title }) 
            </param>
            <param name="then">即能 ThenInclude，还可以二次过滤（这个 EFCore 做不到？）</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.IncludeByPropertyName(System.String)">
            <summary>
            按属性名字符串进行 Include/IncludeMany 操作
            </summary>
            <param name="property"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.IncludeByPropertyNameIf(System.Boolean,System.String)">
            <summary>
            按属性名字符串进行 Include/IncludeMany 操作
            </summary>
            <param name="condition">true 时生效</param>
            <param name="property"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.WithSql(System.String,System.Object)">
            <summary>
            实现 select .. from ( select ... from t ) a 这样的功能<para></para>
            使用 AsTable 方法也可以达到效果<para></para>
            示例：WithSql("select * from id=@id", new { id = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">SQL语句</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.WithMemory``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            实现 select .. from ( select .. UNION ALL select .. ) a 这样的功能（基于内存数据）
            </summary>
            <param name="source">内存数据</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelect`1.WithTempQuery``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            嵌套查询 select * from ( select ... from table ... ) a
            </summary>
            <typeparam name="TDto"></typeparam>
            <param name="selector"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectFromExpression`1.Where(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            查询条件，Where(a => a.Id > 10)，支持导航对象查询，Where(a => a.Author.Email == "<EMAIL>")
            </summary>
            <param name="exp">lambda表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectFromExpression`1.WhereIf(System.Boolean,System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            查询条件，Where(true, a => a.Id > 10)，支导航对象查询，Where(true, a => a.Author.Email == "<EMAIL>")
            </summary>
            <param name="condition">true 时生效</param>
            <param name="exp">lambda表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectFromExpression`1.OrderBy``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            按列排序，OrderBy(a => a.Time)
            </summary>
            <typeparam name="TMember"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectFromExpression`1.OrderByDescending``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            按列倒向排序，OrderByDescending(a => a.Time)
            </summary>
            <param name="column">列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.Having(System.Linq.Expressions.Expression{System.Func{FreeSql.ISelectGroupingAggregate{`0,`1},System.Boolean}})">
            <summary>
            按聚合条件过滤，Having(a => a.Count() > 10)
            </summary>
            <param name="exp">lambda表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.HavingIf(System.Boolean,System.Linq.Expressions.Expression{System.Func{FreeSql.ISelectGroupingAggregate{`0,`1},System.Boolean}})">
            <summary>
            按聚合条件过滤，HavingIf(true, a => a.Count() > 10)
            </summary>
            <param name="condition">true 时生效</param>
            <param name="exp">lambda表达式</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.OrderBy``1(System.Linq.Expressions.Expression{System.Func{FreeSql.ISelectGroupingAggregate{`0,`1},``0}})">
            <summary>
            按列排序，OrderBy(a => a.Time)
            </summary>
            <typeparam name="TMember"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.OrderByDescending``1(System.Linq.Expressions.Expression{System.Func{FreeSql.ISelectGroupingAggregate{`0,`1},``0}})">
            <summary>
            按列倒向排序，OrderByDescending(a => a.Time)
            </summary>
            <param name="column">列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.ToList``1(System.Linq.Expressions.Expression{System.Func{FreeSql.ISelectGroupingAggregate{`0,`1},``0}})">
            <summary>
            执行SQL查询，返回指定字段的记录，记录不存在时返回 Count 为 0 的列表
            </summary>
            <typeparam name="TReturn">返回类型</typeparam>
            <param name="select">选择列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.First``1(System.Linq.Expressions.Expression{System.Func{FreeSql.ISelectGroupingAggregate{`0,`1},``0}})">
            <summary>
             执行SQL查询，返回指定字段的记录的第一条记录，记录不存在时返回 TReturn 默认值
            </summary>
            <typeparam name="TReturn"></typeparam>
            <param name="select"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.Select``1(System.Linq.Expressions.Expression{System.Func{FreeSql.ISelectGroupingAggregate{`0,`1},``0}})">
            <summary>
            【linq to sql】专用方法，不建议直接使用
            </summary>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.ToSql``1(System.Linq.Expressions.Expression{System.Func{FreeSql.ISelectGroupingAggregate{`0,`1},``0}},FreeSql.FieldAliasOptions)">
            <summary>
            返回即将执行的SQL语句
            </summary>
            <typeparam name="TReturn">返回类型</typeparam>
            <param name="select">选择列</param>
            <param name="fieldAlias"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.ToSql(System.String)">
            <summary>
            返回即将执行的SQL语句
            </summary>
            <param name="field">指定字段</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.WithTempQuery``1(System.Linq.Expressions.Expression{System.Func{FreeSql.ISelectGroupingAggregate{`0,`1},``0}})">
            <summary>
            嵌套查询 select * from ( select ... from table group by ... ) a
            </summary>
            <typeparam name="TDto"></typeparam>
            <param name="selector"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.Skip(System.Int32)">
            <summary>
            查询向后偏移行数
            </summary>
            <param name="offset"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.Offset(System.Int32)">
            <summary>
            查询向后偏移行数
            </summary>
            <param name="offset">行数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.Limit(System.Int32)">
            <summary>
            查询多少条数据
            </summary>
            <param name="limit"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.Take(System.Int32)">
            <summary>
            查询多少条数据
            </summary>
            <param name="limit"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.Page(System.Int32,System.Int32)">
            <summary>
            分页
            </summary>
            <param name="pageNumber">第几页</param>
            <param name="pageSize">每页多少</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.Page(FreeSql.Internal.Model.BasePagingInfo)">
            <summary>
            分页
            </summary>
            <param name="pagingInfo">分页信息</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.Count">
            <summary>
            查询的记录数量
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGrouping`2.Count(System.Int64@)">
            <summary>
            查询的记录数量，以参数out形式返回
            </summary>
            <param name="count">返回的变量</param>
            <returns></returns>
        </member>
        <member name="P:FreeSql.ISelectGroupingAggregate`1.Key">
            <summary>
            分组的数据
            </summary>
        </member>
        <member name="M:FreeSql.ISelectGroupingAggregate`1.Count">
            <summary>
            记录总数
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGroupingAggregate`1.Sum``1(``0)">
            <summary>
            求和
            </summary>
            <typeparam name="T3"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGroupingAggregate`1.Avg``1(``0)">
            <summary>
            平均值
            </summary>
            <typeparam name="T3"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGroupingAggregate`1.Max``1(``0)">
            <summary>
            最大值
            </summary>
            <typeparam name="T3"></typeparam>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ISelectGroupingAggregate`1.Min``1(``0)">
            <summary>
            最小值
            </summary>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="P:FreeSql.ISelectGroupingAggregate`2.Value">
            <summary>
            所有元素
            </summary>
        </member>
        <member name="M:FreeSql.IUpdate`1.Join``1(FreeSql.ISelect{``0},System.Linq.Expressions.Expression{System.Func{`0,``0,System.Boolean}})">
            <summary>
            联表更新(危险操作)，支持更复杂的联表更新<para></para>
            fsql.Update&lt;T1&gt;()<para></para>
            .Join(fsql.Select&lt;T1&gt;(), (a, b) => a.id == b.id)<para></para>
            .Set((a, b) => a.name == b.name)<para></para>
            .Set((a, b) => a.time == b.time2)<para></para>
            .ExecuteAffrows();
            </summary>
            <typeparam name="T2"></typeparam>
            <param name="query"></param>
            <param name="on"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.Join``1(System.Linq.Expressions.Expression{System.Func{`0,``0,System.Boolean}})">
            <summary>
            联表更新(危险操作)<para></para>
            fsql.Update&lt;T1&gt;()<para></para>
            .Join&lt;T2&gt;((a, b) => a.id == b.id)<para></para>
            .Set((a, b) => a.name == b.name)<para></para>
            .Set((a, b) => a.time == b.time2)<para></para>
            .ExecuteAffrows();
            </summary>
            <typeparam name="T2"></typeparam>
            <param name="on"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.WithTransaction(System.Data.Common.DbTransaction)">
            <summary>
            指定事务对象
            </summary>
            <param name="transaction"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.WithConnection(System.Data.Common.DbConnection)">
            <summary>
            指定事务对象
            </summary>
            <param name="connection"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.CommandTimeout(System.Int32)">
            <summary>
            命令超时设置(秒)
            </summary>
            <param name="timeout"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.NoneParameter(System.Boolean)">
            <summary>
            不使用参数化，可通过 IFreeSql.CodeFirst.IsNotCommandParameter 全局性设置
            </summary>
            <param name="isNotCommandParameter">是否不使用参数化</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.BatchOptions(System.Int32,System.Int32,System.Boolean)">
            <summary>
            批量执行选项设置，一般不需要使用该方法<para></para>
            各数据库 rows, parameters 限制不一样，默认设置：<para></para>
            MySql 500 3000<para></para>
            PostgreSQL 500 3000<para></para>
            SqlServer 500 2100<para></para>
            Oracle 200 999<para></para>
            Sqlite 200 999<para></para>
            若没有事务传入，内部(默认)会自动开启新事务，保证拆包执行的完整性。
            </summary>
            <param name="rowsLimit">指定根据 rows 上限数量拆分执行</param>
            <param name="parameterLimit">指定根据 parameters 上限数量拆分执行</param>
            <param name="autoTransaction">是否自动开启事务</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.BatchProgress(System.Action{FreeSql.Internal.Model.BatchProgressStatus{`0}})">
            <summary>
            批量执行时，分批次执行的进度状态
            </summary>
            <param name="callback">批量执行时的回调委托</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.SetSource(`0)">
            <summary>
            更新数据，设置更新的实体<para></para>
            注意：实体必须定义主键，并且最终会自动附加条件 where id = source.Id
            </summary>
            <param name="source">实体</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.SetSource(System.Collections.Generic.IEnumerable{`0},System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Boolean)">
            <summary>
            更新数据，设置更新的实体集合<para></para>
            注意：实体必须定义主键，并且最终会自动附加条件 where id in (source.Id)
            </summary>
            <param name="source">实体集合</param>
            <param name="tempPrimarys">根据临时主键更新，a => a.Name | a => new{a.Name,a.Time} | a => new[]{"name","time"}</param>
            <param name="ignoreVersion">忽略 IsVersion 乐观锁版本号</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.SetSourceIgnore(`0,System.Func{System.Object,System.Boolean})">
            <summary>
            更新数据，设置更新的实体，同时设置忽略的列<para></para>
            忽略 null 属性：fsql.Update&lt;T&gt;().SetSourceAndIgnore(item, colval => colval == null)<para></para>
            注意：参数 ignore 与 IUpdate.IgnoreColumns/UpdateColumns 不能同时使用
            </summary>
            <param name="source">实体</param>
            <param name="ignore">属性值忽略判断, true忽略</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.IgnoreColumns(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            忽略的列，IgnoreColumns(a => a.Name) | IgnoreColumns(a => new{a.Name,a.Time}) | IgnoreColumns(a => new[]{"name","time"})<para></para>
            注意：不能与 UpdateColumns 不能同时使用
            </summary>
            <param name="columns">lambda选择列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.IgnoreColumns(System.String[])">
            <summary>
            忽略的列<para></para>
            注意：不能与 UpdateColumns 不能同时使用
            </summary>
            <param name="columns">属性名，或者字段名</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.UpdateColumns(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            指定的列，UpdateColumns(a => a.Name) | UpdateColumns(a => new{a.Name,a.Time}) | UpdateColumns(a => new[]{"name","time"})<para></para>
            注意：不能与 IgnoreColumns 不能同时使用
            </summary>
            <param name="columns">lambda选择列</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.UpdateColumns(System.String[])">
            <summary>
            指定的列<para></para>
            注意：不能与 IgnoreColumns 同时使用
            </summary>
            <param name="columns">属性名，或者字段名</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.Set``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},``0)">
            <summary>
            设置列的新值，Set(a => a.Name, "newvalue")
            </summary>
            <typeparam name="TMember"></typeparam>
            <param name="column">lambda选择列</param>
            <param name="value">新值</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.SetIf``1(System.Boolean,System.Linq.Expressions.Expression{System.Func{`0,``0}},``0)">
            <summary>
            设置列的新值，Set(a => a.Name, "newvalue")
            </summary>
            <typeparam name="TMember"></typeparam>
            <param name="condition">true 时生效</param>
            <param name="column">lambda选择列</param>
            <param name="value">新值</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.Set``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            设置列的新值为基础上增加，格式：Set(a => a.Clicks + 1) 相当于 clicks=clicks+1
            <para></para>
            指定更新，格式：Set(a => new T { Clicks = a.Clicks + 1, Time = DateTime.Now }) 相当于 set clicks=clicks+1,time='2019-06-19....'
            </summary>
            <typeparam name="TMember"></typeparam>
            <param name="exp"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.SetIf``1(System.Boolean,System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            设置列的新值为基础上增加，格式：Set(a => a.Clicks + 1) 相当于 clicks=clicks+1
            <para></para>
            指定更新，格式：Set(a => new T { Clicks = a.Clicks + 1, Time = DateTime.Now }) 相当于 set clicks=clicks+1,time='2019-06-19....'
            </summary>
            <typeparam name="TMember"></typeparam>
            <param name="condition">true 时生效</param>
            <param name="exp"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.SetRaw(System.String,System.Object)">
            <summary>
            设置值，自定义SQL语法，SetRaw("title = @title", new { title = "newtitle" })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">sql语法</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.SetDto(System.Object)">
            <summary>
            设置更新的列<para></para>
            SetDto(new { title = "xxx", clicks = 2 })<para></para>
            SetDto(new Dictionary&lt;string, object&gt; { ["title"] = "xxx", ["clicks"] = 2 })<para></para>
            注意：标记 [Column(CanUpdate = false)] 的属性不会被更新<para></para>
            注意：SetDto 与 IUpdate.IgnoreColumns/UpdateColumns 不能同时使用
            </summary>
            <param name="dto">dto 或 Dictionary&lt;string, object&gt;</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.SetDtoIgnore(System.Object,System.Func{System.Object,System.Boolean})">
            <summary>
            设置更新的列<para></para>
            SetDto(new { title = "xxx", clicks = 2 })<para></para>
            SetDto(new Dictionary&lt;string, object&gt; { ["title"] = "xxx", ["clicks"] = 2 })<para></para>
            注意：标记 [Column(CanUpdate = false)] 的属性不会被更新<para></para>
            注意：SetDto 与 IUpdate.IgnoreColumns/UpdateColumns 不能同时使用
            </summary>
            <param name="dto">dto 或 Dictionary&lt;string, object&gt;</param>
            <param name="ignore">属性值忽略判断, true忽略</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.Where(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            lambda表达式条件，仅支持实体基础成员（不包含导航对象）<para></para>
            若想使用导航对象，请使用 ISelect.ToUpdate() 方法
            </summary>
            <param name="exp">lambda表达式条件</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.WhereIf(System.Boolean,System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            lambda表达式条件，仅支持实体基础成员（不包含导航对象）<para></para>
            若想使用导航对象，请使用 ISelect.ToUpdate() 方法
            </summary>
            <param name="condition">true 时生效</param>
            <param name="exp">lambda表达式条件</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.Where(System.String,System.Object)">
            <summary>
            原生sql语法条件，Where("id = @id", new { id = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">sql语法条件</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.Where(`0)">
            <summary>
            传入实体，将主键作为条件
            </summary>
            <param name="item">实体</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.Where(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            传入实体集合，将主键作为条件
            </summary>
            <param name="items">实体集合</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.WhereDynamic(System.Object,System.Boolean)">
            <summary>
            传入动态条件，如：主键值 | new[]{主键值1,主键值2} | TEntity1 | new[]{TEntity1,TEntity2} | new{id=1}
            </summary>
            <param name="dywhere">主键值、主键值集合、实体、实体集合、匿名对象、匿名对象集合</param>
            <param name="not">是否标识为NOT</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.WhereDynamicFilter(FreeSql.Internal.Model.DynamicFilterInfo)">
            <summary>
            动态过滤条件
            </summary>
            <param name="filter"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.DisableGlobalFilter(System.String[])">
            <summary>
            禁用全局过滤功能，不传参数时将禁用所有
            </summary>
            <param name="name">零个或多个过滤器名字</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.AsTable(System.Func{System.String,System.String})">
            <summary>
            设置表名规则，可用于分库/分表，参数1：默认表名；返回值：新表名；
            </summary>
            <param name="tableRule"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.AsTable(System.String)">
            <summary>
            设置表名
            </summary>
            <param name="tableName"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.AsType(System.Type)">
            <summary>
            动态Type，在使用 Update&lt;object&gt; 后使用本方法，指定实体类型
            </summary>
            <param name="entityType"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.ToSql">
            <summary>
            返回即将执行的SQL语句
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.ExecuteAffrows">
            <summary>
            执行SQL语句，返回影响的行数
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.ExecuteUpdated">
            <summary>
            执行SQL语句，返回更新后的记录<para></para>
            注意：此方法只有 Postgresql/SqlServer/Maridb/Firebird/人大金仓 有效果
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdate`1.ExecuteUpdatedAsync(System.Threading.CancellationToken)">
            <summary>
            执行SQL语句，返回更新后的记录<para></para>
            注意：此方法只有 Postgresql/SqlServer/Maridb/Firebird/人大金仓 有效果
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.WithTransaction(System.Data.Common.DbTransaction)">
            <summary>
            指定事务对象
            </summary>
            <param name="transaction"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.WithConnection(System.Data.Common.DbConnection)">
            <summary>
            指定事务对象
            </summary>
            <param name="connection"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.CommandTimeout(System.Int32)">
            <summary>
            命令超时设置(秒)
            </summary>
            <param name="timeout"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.Set``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},``0)">
            <summary>
            设置列的固定新值，Set(a => a.Name, "newvalue")
            </summary>
            <typeparam name="TMember"></typeparam>
            <param name="column">lambda选择列</param>
            <param name="value">新值</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.SetIf``1(System.Boolean,System.Linq.Expressions.Expression{System.Func{`0,``0}},``0)">
            <summary>
            设置列的固定新值，Set(a => a.Name, "newvalue")
            </summary>
            <typeparam name="TMember"></typeparam>
            <param name="condition">true 时生效</param>
            <param name="column">lambda选择列</param>
            <param name="value">新值</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.Set(System.Linq.Expressions.Expression{System.Func{`0,`1,System.Boolean}})">
            <summary>
            设置列的联表值，格式：<para></para>
            Set((a, b) => a.Clicks == b.xxx)<para></para>
            Set((a, b) => a.Clicks == a.Clicks + 1)
            </summary>
            <param name="exp"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.SetIf(System.Boolean,System.Linq.Expressions.Expression{System.Func{`0,`1,System.Boolean}})">
            <summary>
            设置列的联表值，格式：<para></para>
            Set((a, b) => a.Clicks == b.xxx)<para></para>
            Set((a, b) => a.Clicks == a.Clicks + 1)
            <para></para>
            </summary>
            <param name="condition">true 时生效</param>
            <param name="exp"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.SetRaw(System.String,System.Object)">
            <summary>
            设置值，自定义SQL语法，SetRaw("title = @title", new { title = "newtitle" })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">sql语法</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.Where(System.Linq.Expressions.Expression{System.Func{`0,`1,System.Boolean}})">
            <summary>
            lambda表达式条件，仅支持实体基础成员（不包含导航对象）
            </summary>
            <param name="exp">lambda表达式条件</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.WhereIf(System.Boolean,System.Linq.Expressions.Expression{System.Func{`0,`1,System.Boolean}})">
            <summary>
            lambda表达式条件，仅支持实体基础成员（不包含导航对象）
            </summary>
            <param name="condition">true 时生效</param>
            <param name="exp">lambda表达式条件</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.Where(System.String,System.Object)">
            <summary>
            原生sql语法条件，Where("id = @id", new { id = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">sql语法条件</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.DisableGlobalFilter(System.String[])">
            <summary>
            禁用全局过滤功能，不传参数时将禁用所有
            </summary>
            <param name="name">零个或多个过滤器名字</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.AsTable(System.String,System.String)">
            <summary>
            设置表名
            </summary>
            <param name="tableName"></param>
            <param name="joinTableName"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.ToSql">
            <summary>
            返回即将执行的SQL语句
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IUpdateJoin`2.ExecuteAffrows">
            <summary>
            执行SQL语句，返回影响的行数
            </summary>
            <returns></returns>
        </member>
        <member name="P:FreeSql.IAdo.MasterPool">
            <summary>
            主库连接池
            </summary>
        </member>
        <member name="P:FreeSql.IAdo.SlavePools">
            <summary>
            从库连接池
            </summary>
        </member>
        <member name="P:FreeSql.IAdo.DataType">
            <summary>
            数据库类型
            </summary>
        </member>
        <member name="P:FreeSql.IAdo.ConnectionString">
            <summary>
            UseConnectionString 时候的值
            </summary>
        </member>
        <member name="P:FreeSql.IAdo.SlaveConnectionStrings">
            <summary>
            UseSalve 时候的值
            </summary>
        </member>
        <member name="P:FreeSql.IAdo.Identifier">
            <summary>
            唯一标识
            </summary>
        </member>
        <member name="M:FreeSql.IAdo.Transaction(System.Action)">
            <summary>
            开启事务（不支持异步）
            </summary>
            <param name="handler">事务体 () => {}</param>
        </member>
        <member name="M:FreeSql.IAdo.Transaction(System.Data.IsolationLevel,System.Action)">
            <summary>
            开启事务（不支持异步）
            </summary>
            <param name="isolationLevel"></param>
            <param name="handler">事务体 () => {}</param>
        </member>
        <member name="P:FreeSql.IAdo.TransactionCurrentThread">
            <summary>
            当前线程的事务
            </summary>
        </member>
        <member name="M:FreeSql.IAdo.GetDbParamtersByObject(System.Object)">
            <summary>
            将 new { id = 1 } 或者 Dictionary&lt;string, object&gt; 转换为 DbParameter[]
            </summary>
            <param name="obj">new { id = 1 } 或者 Dictionary&lt;string, object&gt;</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.CommandFluent(System.String,System.Object)">
            <summary>
            SQL 命令执行类，fsql.Ado.CommandFluent("select * from user where age > @age", new { age = 25 })<para></para>
            .WithConnection(connection)<para></para>
            .WithTransaction(transaction)<para></para>
            .WithParameter("age", 25)<para></para>
            .WithParameter("id", 11)<para></para>
            .CommandType(CommandType.Text)<para></para>
            .CommandTimeout(60)<para></para>
            .Query&lt;T&gt;(); 或者 ExecuteNonQuery/ExecuteScalar/ExecuteDataTable/ExecuteDataSet/ExecuteArray
            </summary>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteConnectTest(System.Int32)">
            <summary>
            测试数据库是否连接正确，本方法执行如下命令：<para></para>
            MySql/SqlServer/PostgreSQL/达梦/人大金仓/神通: SELECT 1<para></para>
            Oracle: SELECT 1 FROM dual<para></para>
            </summary>
            <param name="commandTimeout">命令超时设置(秒)</param>
            <returns>true: 成功, false: 失败</returns>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteReader(System.Action{FreeSql.Internal.Model.FetchCallbackArgs{System.Data.Common.DbDataReader}},System.Data.CommandType,System.String,System.Data.Common.DbParameter[])">
            <summary>
            查询，若使用读写分离，查询【从库】条件cmdText.StartsWith("SELECT ")，否则查询【主库】
            </summary>
            <param name="fetchHandler"></param>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteReader(System.Action{FreeSql.Internal.Model.FetchCallbackArgs{System.Data.Common.DbDataReader}},System.String,System.Object)">
            <summary>
            查询，ExecuteReader(dr => {}, "select * from user where age > @age", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="fetchHandler"></param>
            <param name="cmdText"></param>
            <param name="parms"></param>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteArray(System.Data.CommandType,System.String,System.Data.Common.DbParameter[])">
            <summary>
            查询
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteArray(System.String,System.Object)">
            <summary>
            查询，ExecuteArray("select * from user where age > @age", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteDataSet(System.Data.CommandType,System.String,System.Data.Common.DbParameter[])">
            <summary>
            查询
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteDataSet(System.String,System.Object)">
            <summary>
            查询，ExecuteDataSet("select * from user where age > @age; select 2", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteDataTable(System.Data.CommandType,System.String,System.Data.Common.DbParameter[])">
            <summary>
            查询
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteDataTable(System.String,System.Object)">
            <summary>
            查询，ExecuteDataTable("select * from user where age > @age", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteNonQuery(System.Data.CommandType,System.String,System.Data.Common.DbParameter[])">
            <summary>
            在【主库】执行
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteNonQuery(System.String,System.Object)">
            <summary>
            在【主库】执行，ExecuteNonQuery("delete from user where age > @age", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteScalar(System.Data.CommandType,System.String,System.Data.Common.DbParameter[])">
            <summary>
            在【主库】执行
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteScalar(System.String,System.Object)">
            <summary>
            在【主库】执行，ExecuteScalar("select 1 from user where age > @age", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.Query``1(System.Data.CommandType,System.String,System.Data.Common.DbParameter[])">
            <summary>
            执行SQL返回对象集合，Query&lt;User&gt;("select * from user where age > @age", new SqlParameter { ParameterName = "age", Value = 25 })
            </summary>
            <typeparam name="T"></typeparam>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.Query``1(System.String,System.Object)">
            <summary>
            执行SQL返回对象集合，Query&lt;User&gt;("select * from user where age > @age", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <typeparam name="T"></typeparam>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.Query``2(System.Data.CommandType,System.String,System.Data.Common.DbParameter[])">
            <summary>
            执行SQL返回对象集合，Query&lt;User&gt;("select * from user where age > @age; select * from address", new SqlParameter { ParameterName = "age", Value = 25 })
            </summary>
            <typeparam name="T1"></typeparam>
            <typeparam name="T2"></typeparam>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.Query``2(System.String,System.Object)">
            <summary>
            执行SQL返回对象集合，Query&lt;User&gt;("select * from user where age > @age; select * from address", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <typeparam name="T1"></typeparam>
            <typeparam name="T2"></typeparam>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteConnectTestAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            测试数据库是否连接正确，本方法执行如下命令：<para></para>
            MySql/SqlServer/PostgreSQL/达梦/人大金仓/神通: SELECT 1<para></para>
            Oracle: SELECT 1 FROM dual<para></para>
            </summary>
            <param name="commandTimeout">命令超时设置(秒)</param>
            <param name="cancellationToken"></param>
            <returns>true: 成功, false: 失败</returns>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteReaderAsync(System.Func{FreeSql.Internal.Model.FetchCallbackArgs{System.Data.Common.DbDataReader},System.Threading.Tasks.Task},System.Data.CommandType,System.String,System.Data.Common.DbParameter[],System.Threading.CancellationToken)">
            <summary>
            查询，若使用读写分离，查询【从库】条件cmdText.StartsWith("SELECT ")，否则查询【主库】
            </summary>
            <param name="readerHander"></param>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
            <param name="cancellationToken"></param>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteReaderAsync(System.Func{FreeSql.Internal.Model.FetchCallbackArgs{System.Data.Common.DbDataReader},System.Threading.Tasks.Task},System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            查询，ExecuteReaderAsync(dr => {}, "select * from user where age > @age", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="readerHander"></param>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <param name="cancellationToken"></param>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteArrayAsync(System.Data.CommandType,System.String,System.Data.Common.DbParameter[],System.Threading.CancellationToken)">
            <summary>
            查询
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
            <param name="cancellationToken"></param>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteArrayAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            查询，ExecuteArrayAsync("select * from user where age > @age", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteDataSetAsync(System.Data.CommandType,System.String,System.Data.Common.DbParameter[],System.Threading.CancellationToken)">
            <summary>
            查询
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
            <param name="cancellationToken"></param>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteDataSetAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            查询，ExecuteDataSetAsync("select * from user where age > @age; select 2", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteDataTableAsync(System.Data.CommandType,System.String,System.Data.Common.DbParameter[],System.Threading.CancellationToken)">
            <summary>
            查询
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
            <param name="cancellationToken"></param>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteDataTableAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            查询，ExecuteDataTableAsync("select * from user where age > @age", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteNonQueryAsync(System.Data.CommandType,System.String,System.Data.Common.DbParameter[],System.Threading.CancellationToken)">
            <summary>
            在【主库】执行
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
            <param name="cancellationToken"></param>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteNonQueryAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            在【主库】执行，ExecuteNonQueryAsync("delete from user where age > @age", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteScalarAsync(System.Data.CommandType,System.String,System.Data.Common.DbParameter[],System.Threading.CancellationToken)">
            <summary>
            在【主库】执行
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
            <param name="cancellationToken"></param>
        </member>
        <member name="M:FreeSql.IAdo.ExecuteScalarAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            在【主库】执行，ExecuteScalarAsync("select 1 from user where age > @age", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.QueryAsync``1(System.Data.CommandType,System.String,System.Data.Common.DbParameter[],System.Threading.CancellationToken)">
            <summary>
            执行SQL返回对象集合，QueryAsync&lt;User&gt;("select * from user where age > @age", new SqlParameter { ParameterName = "age", Value = 25 })
            </summary>
            <typeparam name="T"></typeparam>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.QueryAsync``1(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            执行SQL返回对象集合，QueryAsync&lt;User&gt;("select * from user where age > @age", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <typeparam name="T"></typeparam>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.QueryAsync``2(System.Data.CommandType,System.String,System.Data.Common.DbParameter[],System.Threading.CancellationToken)">
            <summary>
            执行SQL返回对象集合，Query&lt;User&gt;("select * from user where age > @age; select * from address", new SqlParameter { ParameterName = "age", Value = 25 })
            </summary>
            <typeparam name="T1"></typeparam>
            <typeparam name="T2"></typeparam>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="cmdParms"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IAdo.QueryAsync``2(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            执行SQL返回对象集合，Query&lt;User, Address&gt;("select * from user where age > @age; select * from address", new { age = 25 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <typeparam name="T1"></typeparam>
            <typeparam name="T2"></typeparam>
            <param name="cmdText"></param>
            <param name="parms"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="E:FreeSql.IAop.ParseExpression">
            <summary>
            可自定义解析表达式
            </summary>
        </member>
        <member name="E:FreeSql.IAop.ConfigEntity">
            <summary>
            自定义实体的配置，方便和多个 ORM 共同使用
            </summary>
        </member>
        <member name="E:FreeSql.IAop.ConfigEntityProperty">
            <summary>
            自定义实体的属性配置，方便和多个 ORM 共同使用
            </summary>
        </member>
        <member name="E:FreeSql.IAop.CurdBefore">
            <summary>
            增删查改，执行命令之前触发
            </summary>
        </member>
        <member name="E:FreeSql.IAop.CurdAfter">
            <summary>
            增删查改，执行命令完成后触发
            </summary>
        </member>
        <member name="E:FreeSql.IAop.SyncStructureBefore">
            <summary>
            CodeFirst迁移，执行之前触发
            </summary>
        </member>
        <member name="E:FreeSql.IAop.SyncStructureAfter">
            <summary>
            CodeFirst迁移，执行完成触发
            </summary>
        </member>
        <member name="E:FreeSql.IAop.AuditValue">
            <summary>
            Insert/Update自动值处理
            </summary>
        </member>
        <member name="E:FreeSql.IAop.AuditDataReader">
            <summary>
            ADO.NET DataReader 拦截
            </summary>
        </member>
        <member name="E:FreeSql.IAop.CommandBefore">
            <summary>
            监视数据库命令对象(执行前，调试)
            </summary>
        </member>
        <member name="E:FreeSql.IAop.CommandAfter">
            <summary>
            监视数据库命令对象(执行后，用于监视执行性能)
            </summary>
        </member>
        <member name="E:FreeSql.IAop.TraceBefore">
            <summary>
            跟踪开始
            </summary>
        </member>
        <member name="E:FreeSql.IAop.TraceAfter">
            <summary>
            跟踪结束
            </summary>
        </member>
        <member name="P:FreeSql.Aop.ParseExpressionEventArgs.FreeParse">
            <summary>
            内置解析功能，可辅助您进行解析
            </summary>
        </member>
        <member name="P:FreeSql.Aop.ParseExpressionEventArgs.Expression">
            <summary>
            需要您解析的表达式
            </summary>
        </member>
        <member name="P:FreeSql.Aop.ParseExpressionEventArgs.Result">
            <summary>
            解析后的内容
            </summary>
        </member>
        <member name="P:FreeSql.Aop.ConfigEntityEventArgs.EntityType">
            <summary>
            实体类型
            </summary>
        </member>
        <member name="P:FreeSql.Aop.ConfigEntityEventArgs.ModifyResult">
            <summary>
            实体配置
            </summary>
        </member>
        <member name="P:FreeSql.Aop.ConfigEntityEventArgs.ModifyIndexResult">
            <summary>
            索引配置
            </summary>
        </member>
        <member name="P:FreeSql.Aop.ConfigEntityPropertyEventArgs.EntityType">
            <summary>
            实体类型
            </summary>
        </member>
        <member name="P:FreeSql.Aop.ConfigEntityPropertyEventArgs.Property">
            <summary>
            实体的属性
            </summary>
        </member>
        <member name="P:FreeSql.Aop.ConfigEntityPropertyEventArgs.ModifyResult">
            <summary>
            实体的属性配置
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CurdBeforeEventArgs.Identifier">
            <summary>
            标识符，可将 CurdBefore 与 CurdAfter 进行匹配
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CurdBeforeEventArgs.CurdType">
            <summary>
            操作类型
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CurdBeforeEventArgs.EntityType">
            <summary>
            实体类型
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CurdBeforeEventArgs.Table">
            <summary>
            实体类型的元数据
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CurdBeforeEventArgs.Sql">
            <summary>
            执行的 SQL
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CurdBeforeEventArgs.DbParms">
            <summary>
            参数化命令
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CurdBeforeEventArgs.States">
            <summary>
            状态数据，可与 CurdAfter 共享
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CurdAfterEventArgs.Exception">
            <summary>
            发生的错误
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CurdAfterEventArgs.ExecuteResult">
            <summary>
            执行SQL命令，返回的结果
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CurdAfterEventArgs.ElapsedTicks">
            <summary>
            耗时（单位：Ticks）
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CurdAfterEventArgs.ElapsedMilliseconds">
            <summary>
            耗时（单位：毫秒）
            </summary>
        </member>
        <member name="P:FreeSql.Aop.SyncStructureBeforeEventArgs.Identifier">
            <summary>
            标识符，可将 SyncStructureBeforeEventArgs 与 SyncStructureAfterEventArgs 进行匹配
            </summary>
        </member>
        <member name="P:FreeSql.Aop.SyncStructureBeforeEventArgs.EntityTypes">
            <summary>
            实体类型
            </summary>
        </member>
        <member name="P:FreeSql.Aop.SyncStructureBeforeEventArgs.States">
            <summary>
            状态数据，可与 SyncStructureAfter 共享
            </summary>
        </member>
        <member name="P:FreeSql.Aop.SyncStructureAfterEventArgs.Sql">
            <summary>
            执行的 SQL
            </summary>
        </member>
        <member name="P:FreeSql.Aop.SyncStructureAfterEventArgs.Exception">
            <summary>
            发生的错误
            </summary>
        </member>
        <member name="P:FreeSql.Aop.SyncStructureAfterEventArgs.ElapsedTicks">
            <summary>
            耗时（单位：Ticks）
            </summary>
        </member>
        <member name="P:FreeSql.Aop.SyncStructureAfterEventArgs.ElapsedMilliseconds">
            <summary>
            耗时（单位：毫秒）
            </summary>
        </member>
        <member name="P:FreeSql.Aop.AuditValueEventArgs.AuditValueType">
            <summary>
            类型
            </summary>
        </member>
        <member name="P:FreeSql.Aop.AuditValueEventArgs.Column">
            <summary>
            属性列的元数据
            </summary>
        </member>
        <member name="P:FreeSql.Aop.AuditValueEventArgs.Property">
            <summary>
            反射的属性信息
            </summary>
        </member>
        <member name="P:FreeSql.Aop.AuditValueEventArgs.Value">
            <summary>
            获取实体的属性值，也可以设置实体的属性新值
            </summary>
        </member>
        <member name="P:FreeSql.Aop.AuditValueEventArgs.Object">
            <summary>
            实体对象
            </summary>
        </member>
        <member name="P:FreeSql.Aop.AuditValueEventArgs.ObjectAuditBreak">
            <summary>
            中断实体对象审计<para></para>
            false: 每个实体对象的属性都会审计（默认）<para></para>
            true: 每个实体对象只审计一次
            </summary>
        </member>
        <member name="P:FreeSql.Aop.AuditDataReaderEventArgs.DataReader">
            <summary>
            ADO.NET 数据流读取对象
            </summary>
        </member>
        <member name="P:FreeSql.Aop.AuditDataReaderEventArgs.Index">
            <summary>
            DataReader 对应的 Index 位置
            </summary>
        </member>
        <member name="P:FreeSql.Aop.AuditDataReaderEventArgs.Property">
            <summary>
            DataReader 对应的 PropertyInfo
            </summary>
        </member>
        <member name="P:FreeSql.Aop.AuditDataReaderEventArgs.Value">
            <summary>
            获取 Index 对应的值，也可以设置拦截的新值
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CommandBeforeEventArgs.Identifier">
            <summary>
            标识符，可将 CommandBefore 与 CommandAfter 进行匹配
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CommandBeforeEventArgs.States">
            <summary>
            状态数据，可与 CommandAfter 共享
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CommandAfterEventArgs.Exception">
            <summary>
            发生的错误
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CommandAfterEventArgs.Log">
            <summary>
            执行SQL命令，返回的结果
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CommandAfterEventArgs.ElapsedTicks">
            <summary>
            耗时（单位：Ticks）
            </summary>
        </member>
        <member name="P:FreeSql.Aop.CommandAfterEventArgs.ElapsedMilliseconds">
            <summary>
            耗时（单位：毫秒）
            </summary>
        </member>
        <member name="P:FreeSql.Aop.TraceBeforeEventArgs.Identifier">
            <summary>
            标识符，可将 TraceBeforeEventArgs 与 TraceAfterEventArgs 进行匹配
            </summary>
        </member>
        <member name="P:FreeSql.Aop.TraceBeforeEventArgs.States">
            <summary>
            状态数据，可与 TraceAfter 共享
            </summary>
        </member>
        <member name="P:FreeSql.Aop.TraceAfterEventArgs.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:FreeSql.Aop.TraceAfterEventArgs.Exception">
            <summary>
            发生的错误
            </summary>
        </member>
        <member name="P:FreeSql.Aop.TraceAfterEventArgs.ElapsedTicks">
            <summary>
            耗时（单位：Ticks）
            </summary>
        </member>
        <member name="P:FreeSql.Aop.TraceAfterEventArgs.ElapsedMilliseconds">
            <summary>
            耗时（单位：毫秒）
            </summary>
        </member>
        <member name="P:FreeSql.ICodeFirst.IsAutoSyncStructure">
            <summary>
            【开发环境必备】自动同步实体结构到数据库，程序运行中检查实体表是否存在，然后创建或修改
            </summary>
        </member>
        <member name="P:FreeSql.ICodeFirst.IsSyncStructureToLower">
            <summary>
            转小写同步结构，适用 PostgreSQL
            </summary>
        </member>
        <member name="P:FreeSql.ICodeFirst.IsSyncStructureToUpper">
            <summary>
            转大写同步结构，适用 Oracle/达梦/人大金仓
            </summary>
        </member>
        <member name="P:FreeSql.ICodeFirst.IsConfigEntityFromDbFirst">
            <summary>
            将数据库的主键、自增、索引设置导入，适用 DbFirst 模式，无须在实体类型上设置 [Column(IsPrimary)] 或者 ConfigEntity。此功能目前可用于 mysql/sqlserver/postgresql/oracle。<para></para>
            本功能会影响 IFreeSql 首次访问的速度。<para></para>
            若使用 CodeFirst 创建索引后，又直接在数据库上建了索引，若无本功能下一次 CodeFirst 迁移时数据库上创建的索引将被删除
            </summary>
        </member>
        <member name="P:FreeSql.ICodeFirst.IsNoneCommandParameter">
            <summary>
            不使用命令参数化执行，针对 Insert/Update
            </summary>
        </member>
        <member name="P:FreeSql.ICodeFirst.IsGenerateCommandParameterWithLambda">
            <summary>
            是否生成命令参数化执行，针对 lambda 表达式解析<para></para>
            注意：常量不会参数化，变量才会做参数化<para></para>
            var id = 100;
            fsql.Select&lt;T&gt;().Where(a => a.id == id) 会参数化<para></para>
            fsql.Select&lt;T&gt;().Where(a => a.id == 100) 不会参数化
            </summary>
        </member>
        <member name="P:FreeSql.ICodeFirst.IsLazyLoading">
            <summary>
            延时加载导航属性对象，导航属性需要声明 virtual
            </summary>
        </member>
        <member name="M:FreeSql.ICodeFirst.GetComparisonDDLStatements``1">
            <summary>
            将实体类型与数据库对比，返回DDL语句
            </summary>
            <typeparam name="TEntity"></typeparam>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ICodeFirst.GetComparisonDDLStatements(System.Type[])">
            <summary>
            将实体类型集合与数据库对比，返回DDL语句
            </summary>
            <param name="entityTypes">实体类型</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ICodeFirst.GetComparisonDDLStatements(System.Type,System.String)">
            <summary>
            将实体类型与数据库对比，返回DDL语句（指定表名）
            </summary>
            <param name="entityType">实体类型</param>
            <param name="tableName">指定表名对比</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ICodeFirst.SyncStructure``1">
            <summary>
            同步实体类型到数据库<para></para>
            注意：生产环境中谨慎使用
            </summary>
            <typeparam name="TEntity"></typeparam>
        </member>
        <member name="M:FreeSql.ICodeFirst.SyncStructure(System.Type[])">
            <summary>
            同步实体类型集合到数据库<para></para>
            注意：生产环境中谨慎使用
            </summary>
            <param name="entityTypes"></param>
        </member>
        <member name="M:FreeSql.ICodeFirst.SyncStructure(System.Type,System.String,System.Boolean)">
            <summary>
            同步实体类型到数据库（指定表名）<para></para>
            注意：生产环境中谨慎使用
            </summary>
            <param name="entityType">实体类型</param>
            <param name="tableName">指定表名对比</param>
            <param name="isForceSync">强制同步结构，无视缓存每次都同步</param>
        </member>
        <member name="M:FreeSql.ICodeFirst.GetDbInfo(System.Type)">
            <summary>
            根据 System.Type 获取数据库信息
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ICodeFirst.ConfigEntity``1(System.Action{FreeSql.DataAnnotations.TableFluent{``0}})">
            <summary>
            FreeSql FluentApi 配置实体，方法名与特性相同
            </summary>
            <typeparam name="T"></typeparam>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ICodeFirst.ConfigEntity(System.Type,System.Action{FreeSql.DataAnnotations.TableFluent})">
            <summary>
            FreeSql FluentApi 配置实体，方法名与特性相同
            </summary>
            <param name="type"></param>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.ICodeFirst.GetConfigEntity(System.Type)">
            <summary>
            获取 FreeSql FluentApi 配置实体的元数据
            </summary>
            <param name="type"></param>
            <returns>未使用ConfigEntity配置时，返回null</returns>
        </member>
        <member name="M:FreeSql.ICodeFirst.GetTableByEntity(System.Type)">
            <summary>
            获取实体类核心配置
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDbFirst.GetDatabases">
            <summary>
            获取所有数据库
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDbFirst.GetTablesByDatabase(System.String[])">
            <summary>
            获取指定数据库的表信息，包括表、列详情、主键、唯一键、索引、外键、备注
            </summary>
            <param name="database">可选-默认查询当前数据库</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDbFirst.GetTableByName(System.String,System.Boolean)">
            <summary>
            获取指定单表信息，包括列详情、主键、唯一键、索引、备注
            </summary>
            <param name="name">表名，如：dbo.table1</param>
            <param name="ignoreCase">是否忽略大小写</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDbFirst.ExistsTable(System.String,System.Boolean)">
            <summary>
            判断表是否存在
            </summary>
            <param name="name">表名，如：dbo.table1</param>
            <param name="ignoreCase">是否忽略大小写</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDbFirst.GetDbType(FreeSql.DatabaseModel.DbColumnInfo)">
            <summary>
            获取数据库枚举类型int值
            </summary>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDbFirst.GetCsConvert(FreeSql.DatabaseModel.DbColumnInfo)">
            <summary>
            获取c#转换，(int)、(long)
            </summary>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDbFirst.GetCsTypeValue(FreeSql.DatabaseModel.DbColumnInfo)">
            <summary>
            获取c#值
            </summary>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDbFirst.GetCsType(FreeSql.DatabaseModel.DbColumnInfo)">
            <summary>
            获取c#类型，int、long
            </summary>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDbFirst.GetCsTypeInfo(FreeSql.DatabaseModel.DbColumnInfo)">
            <summary>
            获取c#类型对象
            </summary>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDbFirst.GetDataReaderMethod(FreeSql.DatabaseModel.DbColumnInfo)">
            <summary>
            获取ado.net读取方法, GetBoolean、GetInt64
            </summary>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDbFirst.GetCsStringify(FreeSql.DatabaseModel.DbColumnInfo)">
            <summary>
            序列化
            </summary>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDbFirst.GetCsParse(FreeSql.DatabaseModel.DbColumnInfo)">
            <summary>
            反序列化
            </summary>
            <param name="column"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.IDbFirst.GetEnumsByDatabase(System.String[])">
            <summary>
            获取数据库枚举类型，适用 PostgreSQL
            </summary>
            <param name="database"></param>
            <returns></returns>
        </member>
        <member name="F:FreeSql.Internal.BaseDiyMemberExpression._lambdaParameter">
            <summary>
            临时 LambdaExpression.Parameter
            </summary>
        </member>
        <member name="M:FreeSql.Internal.CommonProvider.InsertOrUpdateProvider`1.SplitSourceByIdentityValueIsNull(System.Collections.Generic.List{`0})">
            <summary>
            如果实体类有自增属性，分成两个 List，有值的Item1 merge，无值的Item2 insert
            </summary>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Internal.CommonProvider.InsertProvider`1.IgnoreCanInsert">
            <summary>
            AsType, Ctor, ClearData 三处地方需要重新加载
            </summary>
        </member>
        <member name="M:FreeSql.Internal.CommonProvider.UpdateProvider`1.IgnoreCanUpdate">
            <summary>
            AsType, Ctor, ClearData 三处地方需要重新加载
            </summary>
        </member>
        <member name="M:FreeSql.Internal.CommonUtils.GetPropertyCommentByDescriptionAttribute(System.Type)">
            <summary>
            动态读取 DescriptionAttribute 注释文本
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Internal.CommonUtils.GetProperyCommentBySummary(System.Type)">
            <summary>
            通过属性的注释文本，通过 xml 读取
            </summary>
            <param name="type"></param>
            <returns>Dict：key=属性名，value=注释</returns>
        </member>
        <member name="P:FreeSql.Internal.DbUpdateVersionException.Table">
            <summary>
            更新实体的元数据
            </summary>
        </member>
        <member name="P:FreeSql.Internal.DbUpdateVersionException.Sql">
            <summary>
            执行更新的 SQL
            </summary>
        </member>
        <member name="P:FreeSql.Internal.DbUpdateVersionException.DbParams">
            <summary>
            执行更新命令的参数
            </summary>
        </member>
        <member name="P:FreeSql.Internal.DbUpdateVersionException.Affrows">
            <summary>
            执行更新命令影响的行
            </summary>
        </member>
        <member name="P:FreeSql.Internal.DbUpdateVersionException.EntitySourceCount">
            <summary>
            更新的实体数量
            </summary>
        </member>
        <member name="P:FreeSql.Internal.DbUpdateVersionException.EntitySource">
            <summary>
            更新的实体
            </summary>
        </member>
        <member name="T:FreeSql.Internal.MappingPriorityType">
            <summary>
            映射优先级，默认： Attribute > FluentApi > Aop
            </summary>
        </member>
        <member name="F:FreeSql.Internal.MappingPriorityType.Attribute">
            <summary>
            实体特性<para></para>
            [Table(Name = "tabname")]<para></para>
            [Column(Name = "table_id")]
            </summary>
        </member>
        <member name="F:FreeSql.Internal.MappingPriorityType.FluentApi">
            <summary>
            流式接口<para></para>
            fsql.CodeFirst.ConfigEntity(a => a.Name("tabname"))<para></para>
            fsql.CodeFirst.ConfigEntity(a => a.Property(b => b.Id).Name("table_id"))
            </summary>
        </member>
        <member name="F:FreeSql.Internal.MappingPriorityType.Aop">
            <summary>
            AOP 特性 https://github.com/dotnetcore/FreeSql/wiki/AOP<para></para>
            fsql.Aop.ConfigEntity += (_, e) => e.ModifyResult.Name = "public.tabname";<para></para>
            fsql.Aop.ConfigEntityProperty += (_, e) => e.ModifyResult.Name = "table_id";<para></para>
            </summary>
        </member>
        <member name="F:FreeSql.Internal.NameConvertType.None">
            <summary>
            不进行任何处理
            </summary>
        </member>
        <member name="F:FreeSql.Internal.NameConvertType.PascalCaseToUnderscore">
            <summary>
            将帕斯卡命名字符串转换为下划线分隔字符串
            <para></para>
            BigApple -> Big_Apple
            </summary>
        </member>
        <member name="F:FreeSql.Internal.NameConvertType.PascalCaseToUnderscoreWithUpper">
            <summary>
            将帕斯卡命名字符串转换为下划线分隔字符串，且转换为全大写
            <para></para>
            BigApple -> BIG_APPLE
            </summary>
        </member>
        <member name="F:FreeSql.Internal.NameConvertType.PascalCaseToUnderscoreWithLower">
            <summary>
            将帕斯卡命名字符串转换为下划线分隔字符串，且转换为全小写
            <para></para>
            BigApple -> big_apple
            </summary>
        </member>
        <member name="F:FreeSql.Internal.NameConvertType.ToUpper">
            <summary>
            将字符串转换为大写
            <para></para>
            BigApple -> BIGAPPLE
            </summary>
        </member>
        <member name="F:FreeSql.Internal.NameConvertType.ToLower">
            <summary>
            将字符串转换为小写
            <para></para>
            BigApple -> bigapple
            </summary>
        </member>
        <member name="M:FreeSql.Internal.GlobalFilter.Apply``1(System.String,System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Boolean)">
            <summary>
            创建一个过滤器<para></para>
            提示：在 Lambda 中判断登陆身份，请参考资料 AsyncLocal
            </summary>
            <typeparam name="TEntity"></typeparam>
            <param name="name">名字</param>
            <param name="where">表达式</param>
            <param name="before">条件在最前面</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Internal.GlobalFilter.ApplyIf``1(System.String,System.Func{System.Boolean},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Boolean)">
            <summary>
            创建一个动态过滤器，当 condition 返回值为 true 时才生效<para></para>
            场景：当登陆身份是管理员，则过滤条件不生效<para></para>
            提示：在 Lambda 中判断登陆身份，请参考资料 AsyncLocal
            </summary>
            <typeparam name="TEntity"></typeparam>
            <param name="name">名字</param>
            <param name="condition">委托，返回值为 true 时才生效</param>
            <param name="where">表达式</param>
            <param name="before">条件在最前面</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Internal.GlobalFilter.ApplyOnly``1(System.String,System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Boolean)">
            <summary>
            创建一个过滤器（实体类型 属于指定 TEntity 才会生效）<para></para>
            提示：在 Lambda 中判断登陆身份，请参考资料 AsyncLocal
            </summary>
            <typeparam name="TEntity"></typeparam>
            <param name="name">名字</param>
            <param name="where">表达式</param>
            <param name="before">条件在最前面</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Internal.GlobalFilter.ApplyOnlyIf``1(System.String,System.Func{System.Boolean},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Boolean)">
            <summary>
            创建一个过滤器（实体类型 属于指定 TEntity 才会生效）<para></para>
            场景：当登陆身份是管理员，则过滤条件不生效<para></para>
            提示：在 Lambda 中判断登陆身份，请参考资料 AsyncLocal
            </summary>
            <typeparam name="TEntity"></typeparam>
            <param name="name">名字</param>
            <param name="condition">委托，返回值为 true 时才生效</param>
            <param name="where">表达式</param>
            <param name="before">条件在最前面</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Internal.Model.AdoCommandFluent.WithConnection(System.Data.Common.DbConnection)">
            <summary>
            使用指定 DbConnection 连接执行
            </summary>
            <param name="conn"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Internal.Model.AdoCommandFluent.WithTransaction(System.Data.Common.DbTransaction)">
            <summary>
            使用指定 DbTransaction 事务执行
            </summary>
            <param name="tran"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Internal.Model.AdoCommandFluent.WithParameter(System.String,System.Object,System.Action{System.Data.Common.DbParameter})">
            <summary>
            增加参数化对象
            </summary>
            <param name="parameterName">参数名</param>
            <param name="value">参数值</param>
            <param name="modify">修改本次创建好的参数化对象，比如将 parameterName 参数修改为 Output 类型</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Internal.Model.AdoCommandFluent.CommandType(System.Data.CommandType)">
            <summary>
            设置执行的命令类型，SQL文本、或存储过程
            </summary>
            <param name="commandType"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Internal.Model.AdoCommandFluent.CommandTimeout(System.Int32)">
            <summary>
            设置命令执行超时（秒）
            </summary>
            <param name="commandTimeout"></param>
            <returns></returns>
        </member>
        <member name="T:FreeSql.Internal.Model.BasePagingInfo">
            <summary>
            分页信息
            </summary>
        </member>
        <member name="P:FreeSql.Internal.Model.BasePagingInfo.PageNumber">
            <summary>
            第几页，从1开始
            </summary>
        </member>
        <member name="P:FreeSql.Internal.Model.BasePagingInfo.PageSize">
            <summary>
            每页多少
            </summary>
        </member>
        <member name="P:FreeSql.Internal.Model.BasePagingInfo.Count">
            <summary>
            查询的记录数量
            </summary>
        </member>
        <member name="P:FreeSql.Internal.Model.BatchProgressStatus`1.Data">
            <summary>
            当前操作的数据
            </summary>
        </member>
        <member name="P:FreeSql.Internal.Model.BatchProgressStatus`1.Current">
            <summary>
            当前批次
            </summary>
        </member>
        <member name="P:FreeSql.Internal.Model.BatchProgressStatus`1.Total">
            <summary>
            总批次数量
            </summary>
        </member>
        <member name="M:FreeSql.Internal.Model.ColumnInfo.GetDbValue(System.Object)">
            <summary>
            获取 obj.CsName 属性值 MapType 之后的数据库值
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Internal.Model.ColumnInfo.GetValue(System.Object)">
            <summary>
            获取 obj.CsName 属性原始值（不经过 MapType）
            </summary>
            <param name="obj"></param>
        </member>
        <member name="M:FreeSql.Internal.Model.ColumnInfo.SetValue(System.Object,System.Object)">
            <summary>
            设置 obj.CsName 属性值
            </summary>
            <param name="obj"></param>
            <param name="val"></param>
        </member>
        <member name="T:FreeSql.Internal.Model.DynamicFilterInfo">
            <summary>
            动态过滤条件
            </summary>
        </member>
        <member name="P:FreeSql.Internal.Model.DynamicFilterInfo.Field">
            <summary>
            属性名：Name<para></para>
            导航属性：Parent.Name<para></para>
            多表：b.Name<para></para>
            </summary>
        </member>
        <member name="P:FreeSql.Internal.Model.DynamicFilterInfo.Operator">
            <summary>
            操作符
            </summary>
        </member>
        <member name="P:FreeSql.Internal.Model.DynamicFilterInfo.Value">
            <summary>
            值
            </summary>
        </member>
        <member name="P:FreeSql.Internal.Model.DynamicFilterInfo.Logic">
            <summary>
            Filters 下的逻辑运算符
            </summary>
        </member>
        <member name="P:FreeSql.Internal.Model.DynamicFilterInfo.Filters">
            <summary>
            子过滤条件，它与当前的逻辑关系是 And<para></para>
            注意：当前 Field 可以留空
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.DynamicFilterOperator.Contains">
            <summary>
            like
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.DynamicFilterOperator.Equal">
            <summary>
            =<para></para>
            Equal/Equals/Eq 效果相同
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.DynamicFilterOperator.Equals">
            <summary>
            =<para></para>
            Equal/Equals/Eq 效果相同
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.DynamicFilterOperator.Eq">
            <summary>
            =<para></para>
            Equal/Equals/Eq 效果相同
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.DynamicFilterOperator.NotEqual">
            <summary>
            &lt;&gt;
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.DynamicFilterOperator.GreaterThan">
            <summary>
            &gt;
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.DynamicFilterOperator.GreaterThanOrEqual">
            <summary>
            &gt;=
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.DynamicFilterOperator.LessThan">
            <summary>
            &lt;
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.DynamicFilterOperator.LessThanOrEqual">
            <summary>
            &lt;=
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.DynamicFilterOperator.Range">
            <summary>
            &gt;= and &lt;<para></para>
            此时 Value 的值格式为逗号分割：value1,value2 或者数组
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.DynamicFilterOperator.DateRange">
            <summary>
            &gt;= and &lt;<para></para>
            此时 Value 的值格式为逗号分割：date1,date2 或者数组<para></para>
            这是专门为日期范围查询定制的操作符，它会处理 date2 + 1，比如：<para></para>
            当 date2 选择的是 2020-05-30，那查询的时候是 &lt; 2020-05-31<para></para>
            当 date2 选择的是 2020-05，那查询的时候是 &lt; 2020-06<para></para>
            当 date2 选择的是 2020，那查询的时候是 &lt; 2021<para></para>
            当 date2 选择的是 2020-05-30 12，那查询的时候是 &lt; 2020-05-30 13<para></para>
            当 date2 选择的是 2020-05-30 12:30，那查询的时候是 &lt; 2020-05-30 12:31<para></para>
            并且 date2 只支持以上 5 种格式 (date1 没有限制)
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.DynamicFilterOperator.Any">
            <summary>
            in (1,2,3)<para></para>
            此时 Value 的值格式为逗号分割：value1,value2,value3... 或者数组
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.DynamicFilterOperator.NotAny">
            <summary>
            not in (1,2,3)<para></para>
            此时 Value 的值格式为逗号分割：value1,value2,value3... 或者数组
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.DynamicFilterOperator.Custom">
            <summary>
            自定义解析，此时 Field 为反射信息，Value 为静态方法的参数(string/Expression)<para></para>
            示范：{ Operator: "Custom", Field: "RawSql webapp1.DynamicFilterCustom,webapp1", Value: "(id,name) in ((1,'k'),(2,'m'))" }<para></para>
            注意：使用者自己承担【注入风险】<para></para>
            静态方法定义示范：<para></para>
            namespace webapp1<para></para>
            {<para></para>
            public class DynamicFilterCustom<para></para>
            {<para></para>
            [DynamicFilterCustom]<para></para>
            public static string RawSql(object sender, string value) => value;<para></para>
            }<para></para>
            }<para></para>
            </summary>
        </member>
        <member name="T:FreeSql.Internal.Model.DynamicFilterCustomAttribute">
            <summary>
            授权 DynamicFilter 支持 Custom 自定义解析
            </summary>
        </member>
        <member name="P:FreeSql.Internal.Model.FetchCallbackArgs`1.IsBreak">
            <summary>
            是否放弃继续读取
            </summary>
        </member>
        <member name="P:FreeSql.Internal.Model.TableRef.RefMiddleEntityType">
            <summary>
            中间表，多对多
            </summary>
        </member>
        <member name="F:FreeSql.Internal.Model.TableRefType.PgArrayToMany">
            <summary>
            PostgreSQL 数组类型专属功能<para></para>
            方式一：select * from Role where Id in (RoleIds)<para></para>
            class User {<para></para>
            ____public int[] RoleIds { get; set; }<para></para>
            ____[Navigate(nameof(RoleIds))]<para></para>
            ____public List&lt;Role&gt; Roles { get; set; }<para></para>
            }<para></para>
            方式二：select * from User where RoleIds @&gt; Id<para></para>
            class Role {<para></para>
            ____public int Id { get; set; }<para></para>
            ____[Navigate(nameof(User.RoleIds))]<para></para>
            ____public List&lt;User&gt; Users { get; set; }<para></para>
            }<para></para>
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.IObjectPool`1.IsAvailable">
            <summary>
            是否可用
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.IObjectPool`1.UnavailableException">
            <summary>
            不可用错误
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.IObjectPool`1.UnavailableTime">
            <summary>
            不可用时间
            </summary>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.IObjectPool`1.SetUnavailable(System.Exception,System.DateTime)">
            <summary>
            将对象池设置为不可用，后续 Get/GetAsync 均会报错，同时启动后台定时检查服务恢复可用
            </summary>
            <param name="exception"></param>
            <param name="lastGetTime"></param>
            <returns>由【可用】变成【不可用】时返回true，否则返回false</returns>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.IObjectPool`1.Statistics">
            <summary>
            统计对象池中的对象
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.IObjectPool`1.StatisticsFullily">
            <summary>
            统计对象池中的对象（完整)
            </summary>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.IObjectPool`1.Get(System.Nullable{System.TimeSpan})">
            <summary>
            获取资源
            </summary>
            <param name="timeout">超时</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.IObjectPool`1.GetAsync">
            <summary>
            获取资源
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.IObjectPool`1.Return(FreeSql.Internal.ObjectPool.Object{`0},System.Boolean)">
            <summary>
            使用完毕后，归还资源
            </summary>
            <param name="obj">对象</param>
            <param name="isReset">是否重新创建</param>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.IPolicy`1.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.IPolicy`1.PoolSize">
            <summary>
            池容量
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.IPolicy`1.SyncGetTimeout">
            <summary>
            默认获取超时设置
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.IPolicy`1.IdleTimeout">
            <summary>
            空闲时间，获取时若超出，则重新创建
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.IPolicy`1.AsyncGetCapacity">
            <summary>
            异步获取排队队列大小，小于等于0不生效
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.IPolicy`1.IsThrowGetTimeoutException">
            <summary>
            获取超时后，是否抛出异常
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.IPolicy`1.IsAutoDisposeWithSystem">
            <summary>
            监听 AppDomain.CurrentDomain.ProcessExit/Console.CancelKeyPress 事件自动释放
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.IPolicy`1.CheckAvailableInterval">
            <summary>
            后台定时检查可用性间隔秒数
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.IPolicy`1.Weight">
            <summary>
            权重
            </summary>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.IPolicy`1.OnCreate">
            <summary>
            对象池的对象被创建时
            </summary>
            <returns>返回被创建的对象</returns>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.IPolicy`1.OnDestroy(`0)">
            <summary>
            销毁对象
            </summary>
            <param name="obj">资源对象</param>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.IPolicy`1.OnGetTimeout">
            <summary>
            从对象池获取对象超时的时候触发，通过该方法统计
            </summary>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.IPolicy`1.OnGet(FreeSql.Internal.ObjectPool.Object{`0})">
            <summary>
            从对象池获取对象成功的时候触发，通过该方法统计或初始化对象
            </summary>
            <param name="obj">资源对象</param>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.IPolicy`1.OnGetAsync(FreeSql.Internal.ObjectPool.Object{`0})">
            <summary>
            从对象池获取对象成功的时候触发，通过该方法统计或初始化对象
            </summary>
            <param name="obj">资源对象</param>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.IPolicy`1.OnReturn(FreeSql.Internal.ObjectPool.Object{`0})">
            <summary>
            归还对象给对象池的时候触发
            </summary>
            <param name="obj">资源对象</param>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.IPolicy`1.OnCheckAvailable(FreeSql.Internal.ObjectPool.Object{`0})">
            <summary>
            检查可用性
            </summary>
            <param name="obj">资源对象</param>
            <returns></returns>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.IPolicy`1.OnAvailable">
            <summary>
            事件：可用时触发
            </summary>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.IPolicy`1.OnUnavailable">
            <summary>
            事件：不可用时触发
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.Object`1.Pool">
            <summary>
            所属对象池
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.Object`1.Id">
            <summary>
            在对象池中的唯一标识
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.Object`1.Value">
            <summary>
            资源对象
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.Object`1.GetTimes">
            <summary>
            被获取的总次数
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.Object`1.LastGetTime">
            最后获取时的时间
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.Object`1.LastReturnTime">
            <summary>
            最后归还时的时间
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.Object`1.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.Object`1.LastGetThreadId">
            <summary>
            最后获取时的线程id
            </summary>
        </member>
        <member name="P:FreeSql.Internal.ObjectPool.Object`1.LastReturnThreadId">
            <summary>
            最后归还时的线程id
            </summary>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.Object`1.ResetValue">
            <summary>
            重置 Value 值
            </summary>
        </member>
        <member name="T:FreeSql.Internal.ObjectPool.ObjectPool`1">
            <summary>
            对象池管理类
            </summary>
            <typeparam name="T">对象类型</typeparam>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.ObjectPool`1.CheckAvailable(System.Int32)">
            <summary>
            后台定时检查可用性
            </summary>
            <param name="interval"></param>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.ObjectPool`1.#ctor(System.Int32,System.Func{`0},System.Action{FreeSql.Internal.ObjectPool.Object{`0}})">
            <summary>
            创建对象池
            </summary>
            <param name="poolsize">池大小</param>
            <param name="createObject">池内对象的创建委托</param>
            <param name="onGetObject">获取池内对象成功后，进行使用前操作</param>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.ObjectPool`1.#ctor(FreeSql.Internal.ObjectPool.IPolicy{`0})">
            <summary>
            创建对象池
            </summary>
            <param name="policy">策略</param>
        </member>
        <member name="M:FreeSql.Internal.ObjectPool.ObjectPool`1.GetFree(System.Boolean)">
            <summary>
            获取可用资源，或创建资源
            </summary>
            <returns></returns>
        </member>
        <member name="F:FreeSql.Internal.Utils.ChacheTableEntityFactory">
            <summary>
            用于解决多实例情况下的静态集合缓存问题
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.AsTable_PropertyName_FormatError(System.Object)">
            <summary>
            [Table(AsTable = "{asTable}")] 特性值格式错误
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.AsTable_PropertyName_NotDateTime(System.Object)">
            <summary>
            [Table(AsTable = xx)] 设置的属性名 {atmGroupsValue} 不是 DateTime 类型
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Available_Failed_Get_Resource(System.Object,System.Object)">
            <summary>
            {name}: Failed to get resource {statistics}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Available_Thrown_Exception(System.Object)">
            <summary>
            {name}: An exception needs to be thrown
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Bad_Expression_Format(System.Object)">
            <summary>
            错误的表达式格式 {column}
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Before_Chunk_Cannot_Use_Select">
            <summary>
            Chunk 功能之前不可使用 Select
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Begin_Transaction_Then_ForUpdate">
            <summary>
            安全起见，请务必在事务开启之后，再使用 ForUpdate
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Cannot_Be_NULL">
            <summary>
            不能为 null
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Cannot_Be_NULL_Name(System.Object)">
            <summary>
            {name} 不能为 null
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Cannot_Match_Property(System.Object)">
            <summary>
            无法匹配 {property}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Cannot_Resolve_ExpressionTree(System.Object)">
            <summary>
            {property} 无法解析为表达式树
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Check_UseConnectionString">
            <summary>
            参数 masterConnectionString 不可为空，请检查 UseConnectionString
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Commit">
            <summary>
            提交
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Connection_Failed_Switch_Servers">
            <summary>
            连接失败，准备切换其他可用服务器
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Custom_Expression_ParsingError(System.Object)">
            <summary>
            自定义表达式解析错误：类型 {exp3MethodDeclaringType} 需要定义 static ThreadLocal&amp;lt;ExpressionCallContext&amp;gt; 字段、字段、字段（重要三次提醒）
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Custom_Reflection_IsNotNull">
            <summary>
            Custom { 反射信息 }不能为空，格式：{ 静态方法名 }{ 空格 }{ 反射信息 }
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Custom_StaticMethodName_IsNotNull">
            <summary>
            Custom { 静态方法名 }不能为空，格式：{ 静态方法名 }{ 空格 }{ 反射信息 }
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Custom_StaticMethodName_NotSet_DynamicFilterCustom(System.Object)">
            <summary>
            Custom 对应的{{ 静态方法名 }}：{fiValueCustomArray} 未设置 [DynamicFilterCustomAttribute] 特性
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.CustomFieldSeparatedBySpaces">
            <summary>
            Custom 要求 Field 应该空格分割，并且长度为 2，格式：{ 静态方法名 }{ 空格 }{ 反射信息 }
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.DataType_AsType_Inconsistent(System.Object,System.Object)">
            <summary>
            操作的数据类型({dataDisplayCsharp}) 与 AsType({tableTypeDisplayCsharp}) 不一致，请检查。
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.DateRange_Comma_Separateda_By2Char">
            <summary>
            DateRange 要求 Value 应该逗号分割，并且长度为 2
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.DateRange_DateFormat_yyyy">
            <summary>
            DateRange 要求 Value[1] 格式必须为：yyyy、yyyy-MM、yyyy-MM-dd、yyyy-MM-dd HH、yyyy、yyyy-MM-dd HH:mm
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.DbUpdateVersionException_RowLevelOptimisticLock(System.Object,System.Object)">
            <summary>
            记录可能不存在，或者【行级乐观锁】版本过旧，更新数量{sourceCount}，影响的行数{affrows}。
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Different_Number_SlaveConnectionString_SlaveWeights">
            <summary>
            SlaveConnectionString 数量与 SlaveWeights 不相同
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Duplicate_ColumnAttribute(System.Object)">
            <summary>
            ColumnAttribute.Name {colattrName} 重复存在，请检查（注意：不区分大小写）
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Duplicate_PropertyName(System.Object)">
            <summary>
            属性名 {pName} 重复存在，请检查（注意：不区分大小写）
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Entity_Must_Primary_Key(System.Object,System.Object)">
            <summary>
            {function} 功能要求实体类 {tableCsName} 必须有主键
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Entity_MySQL_VersionsBelow8_NotSupport_Multiple_PrimaryKeys(System.Object)">
            <summary>
            {tbTypeFullName} 是父子关系，但是 MySql 8.0 以下版本中不支持组合多主键
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Entity_NotParentChild_Relationship(System.Object)">
            <summary>
            {tbTypeFullName} 不是父子关系，无法使用该功能
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.EspeciallySubquery_Cannot_Parsing">
            <summary>
            这个特别的子查询不能解析
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Expression_Error_Use_ParameterExpression(System.Object)">
            <summary>
            表达式错误，它的顶级对象不是 ParameterExpression：{exp}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Expression_Error_Use_Successive_MemberAccess_Type(System.Object)">
            <summary>
            表达式错误，它不是连续的 MemberAccess 类型：{exp}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.ExpressionTree_Convert_Type_Error(System.Object,System.Object,System.Object,System.Object)">
            <summary>
            ExpressionTree 转换类型错误，值({value})，类型({valueTypeFullName})，目标类型({typeFullName})，{exMessage}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Failed_SubTable_FieldValue(System.Object)">
            <summary>
            未能解析分表字段值 {sqlWhere}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Functions_AsTable_NotImplemented(System.Object)">
            <summary>
            AsTable 未实现的功能 {asTable}
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.GBase_NotSupport_OtherThanCommas">
            <summary>
            GBase 暂时不支持逗号以外的分割符
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Generated_Same_SubTable(System.Object)">
            <summary>
            tableName：{tableName} 生成了相同的分表名
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.GetPrimarys_ParameterError_IsNotDictKey(System.Object)">
            <summary>
            GetPrimarys 传递的参数 "{primary}" 不正确，它不属于字典数据的键名
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Has_Specified_Cannot_Specified_Second(System.Object,System.Object)">
            <summary>
            已经指定了 {first}，不能再指定 {second}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Ignored_Check_Confirm_PublicGetSet(System.Object,System.Object)">
            <summary>
            {tb2DbName}.{mp2MemberName} 被忽略，请检查 IsIgnore 设置，确认 get/set 为 public
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Include_ParameterType_Error">
            <summary>
            Include 参数类型错误
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Include_ParameterType_Error_Use_IncludeMany">
            <summary>
            Include 参数类型错误，集合属性请使用 IncludeMany
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Include_ParameterType_Error_Use_MemberAccess">
            <summary>
            Include 参数类型错误，表达式类型应该为 MemberAccess
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.IncludeMany_NotValid_Navigation(System.Object,System.Object)">
            <summary>
            IncludeMany 类型 {tbTypeDisplayCsharp} 的属性 {collMemMemberName} 不是有效的导航属性，提示：IsIgnore = true 不会成为导航属性
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.IncludeMany_ParameterError_OnlyUseOneParameter(System.Object)">
            <summary>
            IncludeMany {navigateSelector} 参数错误，Select 只可以使用一个参数的方法，正确格式：.Select(t =&amp;gt;new TNavigate {{}})
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.IncludeMany_ParameterError_Select_ReturnConsistentType(System.Object,System.Object)">
            <summary>
            IncludeMany {navigateSelector} 参数错误，Select lambda参数返回值必须和 {collMemElementType} 类型一致
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.IncludeMany_ParameterType_Error_Use_MemberAccess">
            <summary>
            IncludeMany 参数1 类型错误，表达式类型应该为 MemberAccess
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.IncludeMany_ParameterTypeError(System.Object)">
            <summary>
            IncludeMany {navigateSelector} 参数类型错误，正确格式： a.collections.Take(1).Where(c =&amp;gt;c.aid == a.id).Select(a=&amp;gt; new TNavigate{{}})
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.InsertInto_No_Property_Selected(System.Object)">
            <summary>
            ISelect.InsertInto() 未选择属性: {displayCsharp}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.InsertInto_TypeError(System.Object)">
            <summary>
            ISelect.InsertInto() 类型错误: {displayCsharp}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.InsertOrUpdate_Must_Primary_Key(System.Object)">
            <summary>
            InsertOrUpdate 功能执行 merge into 要求实体类 {CsName} 必须有主键
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.InsertOrUpdate_NotSuport_Generic_UseEntity(System.Object)">
            <summary>
            InsertOrUpdate&amp;lt;&amp;gt;的泛型参数 不支持 {typeofT1},请传递您的实体类
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Install_FreeSql_Extensions_LazyLoading">
            <summary>
            【延时加载】功能需要安装 FreeSql.Extensions.LazyLoading.dll，可前往 nuget 下载
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.LazyLoading_CompilationError(System.Object,System.Object,System.Object)">
            <summary>
            【延时加载】{trytbTypeName} 编译错误：{exMessage}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.LazyLoading_EntityMustDeclarePublic(System.Object)">
            <summary>
            【延时加载】实体类型 {trytbTypeName} 必须声明为 public
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.ManyToMany_AsSelect_NotSupport_Sum_Avg_etc">
            <summary>
            ManyToMany 导航属性 .AsSelect() 暂时不可用于 Sum/Avg/Max/Min/First/ToOne/ToList 方法
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.ManyToMany_NotFound_CorrespondingField(System.Object,System.Object,System.Object,System.Object,System.Object)">
            <summary>
            【ManyToMany】导航属性 {trytbTypeName}.{pnvName} 在 {tbmidCsName} 中没有找到对应的字段，如：{midTypePropsTrytbName}{findtrytbPkCsName}、{midTypePropsTrytbName}_{findtrytbPkCsName}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.ManyToMany_ParsingError_EntityMissing_PrimaryKey(System.Object,System.Object,System.Object)">
            <summary>
            【ManyToMany】导航属性 {trytbTypeName}.{pnvName} 解析错误，实体类型 {tbrefTypeName} 缺少主键标识，[Column(IsPrimary = true)]
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.ManyToMany_ParsingError_EntityMustHas_NavigateCollection(System.Object,System.Object,System.Object)">
            <summary>
            【ManyToMany】导航属性 {trytbTypeName}.{pnvName} 解析错误，实体类型 {tbrefTypeName} 必须存在对应的 [Navigate(ManyToMany = x)] 集合属性
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.ManyToMany_ParsingError_InconsistentType(System.Object,System.Object,System.Object,System.Object,System.Object,System.Object)">
            <summary>
            【ManyToMany】导航属性 {trytbTypeName}.{pnvName} 解析错误，{tbmidCsName}.{trycolCsName} 和 {trytbCsName}.{trytbPrimarysCsName} 类型不一致
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.ManyToMany_ParsingError_IntermediateClass_ErrorMessage(System.Object,System.Object,System.Object,System.Object,System.Object)">
            <summary>
            【ManyToMany】导航属性 {trytbTypeName}.{pnvName} 解析错误，中间类 {tbmidCsName}.{midTypePropsTrytbName} 错误：{exMessage}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.ManyToMany_ParsingError_IntermediateClass_NotManyToOne_OneToOne(System.Object,System.Object,System.Object,System.Object)">
            <summary>
            【ManyToMany】导航属性 {trytbTypeName}.{pnvName} 解析错误，中间类 {tbmidCsName}.{midTypePropsTrytbName} 导航属性不是【ManyToOne】或【OneToOne】
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.ManyToMany_ParsingError_InconsistentClass_PrimaryKeyError(System.Object,System.Object,System.Object,FreeSql.Internal.Model.ColumnInfo[])">
            <summary>
            导航属性 {trytbTypeName}.{pnvName} 解析错误，中间类主键错误：{tbmidCsName}({string.Join(",", tbmidPrimarys.Select(a => a.CsName))}) 与两边不匹配
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Mapping_Exception_HasNo_SamePropertyName(System.Object)">
            <summary>
            映射异常：{name} 没有一个属性名相同
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.MasterPool_IsNull_UseTransaction">
            <summary>
            Ado.MasterPool 值为 null，该操作无法自启用事务，请显式传递【事务对象】解决
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Missing_FreeSqlProvider_Package(System.Object)">
            <summary>
            缺少 FreeSql 数据库实现包：FreeSql.Provider.{Provider}.dll，可前往 nuget 下载
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Missing_FreeSqlProvider_Package_Reason(System.Object,System.Object)">
            <summary>
            缺少 FreeSql 数据库实现包：{dll}，可前往 nuget 下载；如果存在 {dll} 依然报错（原因是环境问题导致反射不到类型），请在 UseConnectionString/UseConnectionFactory 第三个参数手工传入 typeof({providerType})
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Navigation_Bind_Number_Different(System.Object,System.Object,System.Object,System.Object)">
            <summary>
            导航属性 {trytbTypeName}.{pnvName} 特性 [Navigate] Bind 数目({bindColumnsCount}) 与 外部主键数目({tbrefPrimarysLength}) 不相同
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Navigation_Missing_AsSelect(System.Object,System.Object)">
            <summary>
            {tb2DbName}.{mp2MemberName} 导航属性集合忘了 .AsSelect() 吗？如果在 ToList(a =&amp;gt; a.{mp2MemberName}) 中使用，请移步参考 IncludeMany 文档。
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Navigation_Missing_SetProperty(System.Object,System.Object)">
            <summary>
            【导航属性】{trytbTypeDisplayCsharp}.{pName} 缺少 set 属性
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Navigation_NotFound_CorrespondingField(System.Object,System.Object,System.Object)">
            <summary>
            导航属性 {trytbTypeName}.{pnvName} 没有找到对应的字段，如：{pnvName}{findtbrefPkCsName}、{pnvName}_{findtbrefPkCsName}。或者使用 [Navigate] 特性指定关系映射。
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Navigation_ParsingError_EntityMissingPrimaryKey(System.Object,System.Object,System.Object)">
            <summary>
            导航属性 {trytbTypeName}.{pnvName} 解析错误，实体类型 {trytcTypeName} 缺少主键标识，[Column(IsPrimary = true)]
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Navigation_ParsingError_InconsistentType(System.Object,System.Object,System.Object,System.Object,System.Object,System.Object)">
            <summary>
            导航属性 {trytbTypeName}.{pnvName} 解析错误，{trytbCsName}.{trycolCsName} 和 {tbrefCsName}.{tbrefPrimarysCsName} 类型不一致
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Navigation_ParsingError_NotFound_Property(System.Object,System.Object,System.Object,System.Object)">
            <summary>
            导航属性 {trytbTypeName}.{pnvName} 特性 [Navigate] 解析错误，在 {tbrefTypeName} 未找到属性：{bi}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.NoPrimaryKey_UseSetDto(System.Object)">
            <summary>
            {tableTypeDisplayCsharp} 没有定义主键，无法使用 SetSource，请尝试 SetDto 或者 SetSource 指定临时主键
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.NoProperty_Defined">
            <summary>
             没有定义属性 
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Not_Implemented">
            <summary>
            未实现
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Not_Implemented_Expression(System.Object)">
            <summary>
            未实现函数表达式 {exp} 解析
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Not_Implemented_Expression_ParameterUseConstant(System.Object,System.Object)">
            <summary>
            未实现函数表达式 {exp} 解析，参数 {expArguments} 必须为常量
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Not_Implemented_Expression_UseAsSelect(System.Object,System.Object,System.Object)">
            <summary>
            未实现函数表达式 {exp} 解析，如果正在操作导航属性集合，请使用 .AsSelect().{exp3MethodName}({exp3ArgumentsCount})
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Not_Implemented_MemberAcess_Constant">
            <summary>
            未实现 MemberAccess 下的 Constant
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Not_Implemented_Name(System.Object)">
            <summary>
            未实现 {name}
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Not_Support">
            <summary>
            不支持
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Not_Support_OrderByRandom(System.Object)">
            <summary>
            {dataType} 不支持 OrderByRandom 随机排序
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Not_Valid_Navigation_Property(System.Object)">
            <summary>
            {property} 不是有效的导航属性
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.NotFound_Column(System.Object,System.Object)">
            <summary>
            {dbName} 找不到列 {memberName}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.NotFound_CsName_Column(System.Object)">
            <summary>
            找不到 {CsName} 对应的列
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.NotFound_Property(System.Object)">
            <summary>
            找不到属性：{memberName}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.NotFound_PropertyName(System.Object)">
            <summary>
            找不到属性名 {proto}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.NotFound_Reflection(System.Object)">
            <summary>
            Custom 找不到对应的{{ 反射信息 }}：{fiValueCustomArray}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.NotFound_Static_MethodName(System.Object)">
            <summary>
            Custom 找不到对应的{{ 静态方法名 }}：{fiValueCustomArray}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.NotFound_Table_Property_AsTable(System.Object)">
            <summary>
            [Table(AsTable = xx)] 设置的属性名 {atmGroupsValue} 不存在
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.NotSpecified_UseConnectionString_UseConnectionFactory">
            <summary>
            未指定 UseConnectionString 或者 UseConnectionFactory
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.ObjectPool_Get_Timeout(System.Object,System.Object,System.Object)">
            <summary>
            【{policyName}】ObjectPool.{GetName}() timeout {totalSeconds} seconds, see: https://github.com/dotnetcore/FreeSql/discussions/1081
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.ObjectPool_GetAsync_Queue_Long(System.Object,System.Object)">
            <summary>
            【{policyName}】ObjectPool.GetAsync() The queue is too long. Policy.AsyncGetCapacity = {asyncGetCapacity}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.OneToMany_NotFound_CorrespondingField(System.Object,System.Object,System.Object,System.Object,System.Object)">
            <summary>
            【OneToMany】导航属性 {trytbTypeName}.{pnvName} 在 {tbrefCsName} 中没有找到对应的字段，如：{findtrytb}{findtrytbPkCsName}、{findtrytb}_{findtrytbPkCsName}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.OneToMany_ParsingError_InconsistentType(System.Object,System.Object,System.Object,System.Object,System.Object,System.Object)">
            <summary>
            【OneToMany】导航属性 {trytbTypeName}.{pnvName} 解析错误，{trytbCsName}.{trytbPrimarysCsName} 和 {tbrefCsName}.{trycolCsName} 类型不一致
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.OneToMany_UseNavigate(System.Object,System.Object)">
            <summary>
            、{refpropName}{findtrytbPkCsName}、{refpropName}_{findtrytbPkCsName}。或者使用 [Navigate] 特性指定关系映射。
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Parameter_Field_NotSpecified">
            <summary>
            参数 field 未指定
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.ParameterError_NotValid_Collection(System.Object)">
            <summary>
            {property} 参数错误，它不是集合属性，必须为 IList&amp;lt;T&amp;gt; 或者 ICollection&amp;lt;T&amp;gt;
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.ParameterError_NotValid_Navigation(System.Object)">
            <summary>
            {property} 参数错误，它不是有效的导航属性
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.ParameterError_NotValid_PropertyName(System.Object,System.Object,System.Object)">
            <summary>
            {where} 参数错误，{keyval} 不是有效的属性名，在实体类 {reftbTypeDisplayCsharp} 无法找到
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.ParameterError_NotValid_UseCommas(System.Object)">
            <summary>
            {property} 参数错误，格式 "TopicId=Id，多组使用逗号连接" 
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Parsing_Failed(System.Object,System.Object)">
            <summary>
            解析失败 {callExpMethodName} {message}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Policy_ObjectPool_Dispose(System.Object)">
            <summary>
            【{policyName}】The ObjectPool has been disposed, see: https://github.com/dotnetcore/FreeSql/discussions/1079
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Policy_Status_NotAvailable(System.Object,System.Object)">
            <summary>
            【{policyName}】状态不可用，等待后台检查程序恢复方可使用。{UnavailableExceptionMessage}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Properties_AsRowLock_Must_Numeric_Byte(System.Object)">
            <summary>
            属性{trytbVersionColumnCsName} 被标注为行锁（乐观锁）(IsVersion)，但其必须为数字类型 或者 byte[] 或者 string，并且不可为 Nullable
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Properties_Cannot_Null">
            <summary>
            properties 参数不能为空
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Property_Cannot_Find(System.Object)">
            <summary>
            {property} 属性名无法找到
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Range_Comma_Separateda_By2Char">
            <summary>
            Range 要求 Value 应该逗号分割，并且长度为 2
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.RollBack">
            <summary>
            回滚
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.RunTimeError_Reflection_IncludeMany">
            <summary>
            运行时错误，反射获取 IncludeMany 方法失败
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Set_Column_IsNullable_False(System.Object)">
            <summary>
            {qoteSql} is NULL，除非设置特性 [Column(IsNullable = false)]
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.SubTableFieldValue_CannotLessThen(System.Object,System.Object)">
            <summary>
            分表字段值 "{dt}" 不能小于 "{beginTime} "
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.SubTableFieldValue_IsNotNull">
            <summary>
            分表字段值不能为 null
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.SubTableFieldValue_NotConvertDateTime(System.Object)">
            <summary>
            分表字段值 "{columnValue}" 不能转化成 DateTime
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.SubTableFieldValue_NotMatchTable(System.Object)">
            <summary>
            分表字段值 "{dt}" 未匹配到分表名
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.T2_Type_Error">
            <summary>
            T2 类型错误
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.TableName_Format_Error(System.Object)">
            <summary>
            tableName 格式错误，示例：“log_{yyyyMMdd}”
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Type_AsType_Parameter_Error(System.Object)">
            <summary>
            {Type}.AsType 参数错误，请传入正确的实体类型
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Type_Cannot_Access_Constructor(System.Object)">
            <summary>
            {thatFullName} 类型无法访问构造函数
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Type_Error_Name(System.Object)">
            <summary>
            {name} 类型错误
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.TypeAsType_NotSupport_Object(System.Object)">
            <summary>
            {Type}.AsType 参数不支持指定为 object
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.TypeError_CannotUse_IncludeMany(System.Object)">
            <summary>
            类型 {typeofFullName} 错误，不能使用 IncludeMany
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Unable_Parse_Expression(System.Object)">
            <summary>
            无法解析表达式：{exp}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.Unable_Parse_ExpressionMethod(System.Object)">
            <summary>
            无法解析表达式方法 {exp3tmpCallMethodName}
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.Use_InsertDict_Method">
            <summary>
            请使用 fsql.InsertDict(dict) 方法插入字典数据
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_NotFound_Name(System.Object)">
            <summary>
            找不到 {name}
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_SlaveDatabase">
            <summary>
            从库
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_MasterDatabase">
            <summary>
            主库
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_Access_InsertOnlyOneAtTime">
            <summary>
            蛋疼的 Access 插入只能一条一条执行，不支持 values(..),(..) 也不支持 select .. UNION ALL select ..
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_BaseEntity_Initialization_Error">
            <summary>
            BaseEntity.Initialization 初始化错误，获取到 IFreeSql 是 null
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_BlockAccess_WaitForRecovery(System.Object,System.Object)">
            <summary>
            【{thisName}】Block access and wait for recovery: {exMessage}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_CannotBeConverted_To_ISelect(System.Object)">
            <summary>
            无法将 IQueryable&amp;lt;{typeofName}&amp;gt; 转换为 ISelect&amp;lt;{typeofName}&amp;gt;，因为他的实现不是 FreeSql.Extensions.Linq.QueryableProvider
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_ConnectionStringError">
            <summary>
            连接字符串错误
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_ConnectionStringError_Check(System.Object)">
            <summary>
            【{thisName}】连接字符串错误，请检查。
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_ConnectionStringError_CheckProject">
            <summary>
            连接字符串错误，或者检查项目属性 &amp;gt; 生成 &amp;gt; 目标平台：x86 | x64，或者改用 FreeSql.Provider.SqliteCore 访问 arm 平台
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_ConnectionStringError_CheckProjectConnection(System.Object)">
            <summary>
            【{thisName}】连接字符串错误，请检查。或者检查项目属性 &amp;gt; 生成 &amp;gt; 目标平台：x86 | x64，或者改用 FreeSql.Provider.SqliteCore 访问 arm 平台
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_CustomAdapter_Cannot_Use_CreateCommand">
            <summary>
            FreeSql.Provider.CustomAdapter 无法使用 CreateCommand
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_CustomAdapter_OnlySuppport_UseConnectionFactory">
            <summary>
            FreeSql.Provider.CustomAdapter 仅支持 UseConnectionFactory 方式构建 IFreeSql
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_Dameng_NotSupport_TablespaceSchemas(System.Object)">
            <summary>
            达梦 CodeFirst 不支持代码创建 tablespace 与 schemas {tbname}
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_DB_Parameter_Error_NoConnectionString">
            <summary>
            -DB 参数错误，未提供 ConnectionString
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_DB_ParameterError">
            <summary>
            -DB 参数错误，格式为：MySql,ConnectionString
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_DB_ParameterError_UnsupportedType(System.Object)">
            <summary>
            -DB 参数错误，不支持的类型："{dbargs}"
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_Features_Unique(System.Object,System.Object)">
            <summary>
            {method} 是 FreeSql.Provider.{provider} 特有的功能
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_InsertOrUpdate_Unable_UpdateColumns">
            <summary>
            fsql.InsertOrUpdate Sqlite 无法完成 UpdateColumns 操作
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_MygisGeometry_NotImplement(System.Object)">
            <summary>
            MygisGeometry.Parse 未实现 "{wkt}"
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_NameOptions_Incorrect">
            <summary>
            -NameOptions 参数错误，格式为：0,0,0,0
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_Not_Implemented_Feature">
            <summary>
             未实现该功能
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_Not_Implemented_FeedBack">
            <summary>
            未实现错误，请反馈给作者
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_NotImplementSkipOffset(System.Object)">
            <summary>
            FreeSql.Provider.{providerName} 未实现 Skip/Offset 功能，如果需要分页请使用判断上一次 id
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_OldTableExists(System.Object,System.Object)">
            <summary>
            旧表(OldName)：{tboldname} 存在，数据库已存在 {tbname} 表，无法改名
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_OnConflictDoUpdate_MustIsPrimary">
            <summary>
            OnConflictDoUpdate 功能要求实体类必须设置 IsPrimary 属性
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_Oracle_NotSupport_TablespaceSchemas(System.Object)">
            <summary>
            Oracle CodeFirst 不支持代码创建 tablespace 与 schemas {tbname}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_ParsingFailed_UseRestoreToSelect(System.Object,System.Object)">
            <summary>
            解析失败 {callExpMethodName} {message}，提示：可以使用扩展方法 IQueryable.RestoreToSelect() 还原为 ISelect 再查询
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_RequiresEntityPrimaryKey(System.Object,System.Object)">
            <summary>
            fsql.InsertOrUpdate + IfExistsDoNothing + {providerName}要求实体类 {tableCsName} 必须有主键
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_SelectManayErrorType(System.Object)">
            <summary>
            SelectMany 错误的类型：{typeFullName}
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_Type_IsNot_Migrable(System.Object)">
            <summary>
            类型 {objentityTypeFullName} 不可迁移
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_Type_IsNot_Migrable_0Attributes(System.Object)">
            <summary>
            类型 {objentityTypeFullName} 不可迁移，可迁移属性0个
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_TypeMappingNotImplemented(System.Object)">
            <summary>
            未实现 {columnDbTypeTextFull} 类型映射
            </summary>
        </member>
        <member name="M:FreeSql.CoreErrorStrings.S_WrongParameter(System.Object)">
            <summary>
            错误的参数设置：{args}
            </summary>
        </member>
        <member name="P:FreeSql.CoreErrorStrings.S_ObjectPool">
            <summary>
            对象池
            </summary>
        </member>
        <member name="M:FreeSqlGlobalExpressionCallExtensions.Between(System.DateTime,System.DateTime,System.DateTime)">
            <summary>
            C#： that >= between &amp;&amp; that &lt;= and<para></para>
            SQL： that BETWEEN between AND and
            </summary>
            <param name="that"></param>
            <param name="between"></param>
            <param name="and"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExpressionCallExtensions.BetweenEnd(System.DateTime,System.DateTime,System.DateTime)">
            <summary>
            注意：这个方法和 Between 有细微区别<para></para>
            C#： that >= start &amp;&amp; that &lt; end<para></para>
            SQL： that >= start and that &lt; end
            </summary>
            <param name="that"></param>
            <param name="start"></param>
            <param name="end"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExpressionCallExtensions.In``1(``0,``0)">
            <summary>
            field IN (value1)
            </summary>
        </member>
        <member name="M:FreeSqlGlobalExpressionCallExtensions.In``1(``0,``0,``0)">
            <summary>
            field in (value1, value2)
            </summary>
        </member>
        <member name="M:FreeSqlGlobalExpressionCallExtensions.In``1(``0,``0,``0,``0)">
            <summary>
            field in (value1, value2, value3)
            </summary>
        </member>
        <member name="M:FreeSqlGlobalExpressionCallExtensions.In``1(``0,``0,``0,``0,``0)">
            <summary>
            field in (value1, value2, value3, value4)
            </summary>
        </member>
        <member name="M:FreeSqlGlobalExpressionCallExtensions.In``1(``0,``0,``0,``0,``0,``0)">
            <summary>
            field in (value1, value2, value3, value4, value5)
            </summary>
        </member>
        <member name="M:FreeSqlGlobalExpressionCallExtensions.In``1(``0,``0[])">
            <summary>
            field in (values)
            </summary>
        </member>
        <member name="M:FreeSqlGlobalExtensions.DisplayCsharp(System.Type,System.Boolean)">
            <summary>
            获取 Type 的原始 c# 文本表示
            </summary>
            <param name="type"></param>
            <param name="isNameSpace"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.Distance(System.Drawing.Point,System.Drawing.Point)">
            <summary>
            测量两个经纬度的距离，返回单位：米
            </summary>
            <param name="that">经纬坐标1</param>
            <param name="point">经纬坐标2</param>
            <returns>返回距离（单位：米）</returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.AsSelect``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            将 IEnumable&lt;T&gt; 转成 ISelect&lt;T&gt;，以便使用 FreeSql 的查询功能。此方法用于 Lambda 表达式中，快速进行集合导航的查询。
            </summary>
            <typeparam name="TEntity"></typeparam>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.Select``2(IFreeSql)">
            <summary>
            多表查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.Clone``1(FreeSql.ISelect{``0})">
            <summary>
            克隆 ISelect
            </summary>
        </member>
        <member name="M:FreeSqlGlobalExtensions.Clone``2(FreeSql.ISelect{``0,``1})">
            <summary>
            克隆 ISelect
            </summary>
        </member>
        <member name="M:FreeSqlGlobalExtensions.Clone``3(FreeSql.ISelect{``0,``1,``2})">
            <summary>
            克隆 ISelect
            </summary>
        </member>
        <member name="M:FreeSqlGlobalExtensions.IncludeMany``2(System.Collections.Generic.List{``0},IFreeSql,System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1}}},System.Action{FreeSql.ISelect{``1}})">
            <summary>
            本方法实现从已知的内存 List 数据，进行和 ISelect.IncludeMany 相同功能的贪婪加载<para></para>
            示例：new List&lt;Song&gt;(new[] { song1, song2, song3 }).IncludeMany(fsql, a => a.Tags);<para></para>
            文档：https://github.com/dotnetcore/FreeSql/wiki/%E8%B4%AA%E5%A9%AA%E5%8A%A0%E8%BD%BD
            </summary>
            <typeparam name="T1"></typeparam>
            <typeparam name="TNavigate"></typeparam>
            <param name="list"></param>
            <param name="orm"></param>
            <param name="navigateSelector">选择一个集合的导航属性，如： .IncludeMany(a => a.Tags)<para></para>
            可以 .Where 设置临时的关系映射，如： .IncludeMany(a => a.Tags.Where(tag => tag.TypeId == a.Id))<para></para>
            可以 .Take(5) 每个子集合只取5条，如： .IncludeMany(a => a.Tags.Take(5))<para></para>
            可以 .Select 设置只查询部分字段，如： (a => new TNavigate { Title = a.Title }) 
            </param>
            <param name="then">即能 ThenInclude，还可以二次过滤（这个 EFCore 做不到？）</param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.IncludeByPropertyName``1(System.Collections.Generic.List{``0},IFreeSql,System.String,System.String,System.Int32,System.String,System.Linq.Expressions.Expression{System.Action{FreeSql.ISelect{System.Object}}})">
            <summary>
            本方法实现从已知的内存 List 数据，进行和 ISelect.IncludeMany/Include 相同功能的贪婪加载<para></para>
            集合：new List&lt;Song&gt;(new[] { song1, song2, song3 }).IncludeByPropertyName(fsql, "Tags", "ParentId=Id", 5, "Id,Name");<para></para>
            普通：new List&lt;Song&gt;(new[] { song1, song2, song3 }).IncludeByPropertyName(fsql, "Catetory"); <para></para>
            －－－普通属性 where/take/select 参数将无效<para></para>
            文档：https://github.com/dotnetcore/FreeSql/wiki/%E8%B4%AA%E5%A9%AA%E5%8A%A0%E8%BD%BD
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="list"></param>
            <param name="orm"></param>
            <param name="property">选择一个集合或普通属性</param>
            <param name="where">设置临时的子集合关系映射，格式：子类属性=T1属性，多组以逗号分割</param>
            <param name="take">设置子集合只取条数</param>
            <param name="select">设置子集合只查询部分字段</param>
            <returns></returns>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:FreeSqlGlobalExtensions.ToTreeList``1(FreeSql.ISelect{``0})">
            <summary>
            查询数据，加工为树型 List 返回<para></para>
            注意：实体需要配置父子导航属性
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.AsTreeCte``1(FreeSql.ISelect{``0},System.Linq.Expressions.Expression{System.Func{``0,System.String}},System.Boolean,System.String,System.Int32)">
            <summary>
            使用递归 CTE 查询树型的所有子记录，或者所有父记录。<para></para>
            通过测试的数据库：MySql8.0、SqlServer、PostgreSQL、Oracle、Sqlite、Firebird、达梦、人大金仓、翰高<para></para>
            返回隐藏字段：.ToList(a =&gt; new { item = a, level = "a.cte_level", path = "a.cte_path" })<para></para>
            * v2.0.0 兼容 MySql5.6 向上或向下查询，但不支持 pathSelector/pathSeparator 详细：https://github.com/dotnetcore/FreeSql/issues/536
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="that"></param>
            <param name="up">false(默认)：由父级向子级的递归查询<para></para>true：由子级向父级的递归查询</param>
            <param name="pathSelector">路径内容选择</param>
            <param name="pathSeparator">连接路径内容</param>
            <param name="level">递归层级</param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.OrderByRandom``2(FreeSql.ISelect0{``0,``1})">
            <summary>
            随机排序<para></para>
            支持：MySql/SqlServer/PostgreSQL/Oracle/Sqlite/Firebird/DuckDB/达梦/金仓/神通<para></para>
            不支持：MsAcess
            </summary>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.InsertDict(IFreeSql,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            插入数据字典 Dictionary&lt;string, object&gt;
            </summary>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.InsertDict(IFreeSql,System.Collections.Generic.IEnumerable{System.Collections.Generic.Dictionary{System.String,System.Object}})">
            <summary>
            插入数据字典，传入 Dictionary&lt;string, object&gt; 集合
            </summary>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.UpdateDict(IFreeSql,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            更新数据字典 Dictionary&lt;string, object&gt;
            </summary>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.UpdateDict(IFreeSql,System.Collections.Generic.IEnumerable{System.Collections.Generic.Dictionary{System.String,System.Object}})">
            <summary>
            更新数据字典，传入 Dictionary&lt;string, object&gt; 集合
            </summary>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.InsertOrUpdateDict(IFreeSql,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            插入或更新数据字典，此功能依赖数据库特性（低版本可能不支持），参考如下：<para></para>
            MySql 5.6+: on duplicate key update<para></para>
            PostgreSQL 9.4+: on conflict do update<para></para>
            SqlServer 2008+: merge into<para></para>
            Oracle 11+: merge into<para></para>
            Sqlite: replace into<para></para>
            DuckDB: on conflict do update<para></para>
            达梦: merge into<para></para>
            人大金仓：on conflict do update<para></para>
            神通：merge into<para></para>
            MsAccess：不支持<para></para>
            </summary>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.DeleteDict(IFreeSql,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            删除数据字典 Dictionary&lt;string, object&gt;
            </summary>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.DeleteDict(IFreeSql,System.Collections.Generic.IEnumerable{System.Collections.Generic.Dictionary{System.String,System.Object}})">
            <summary>
            删除数据字典，传入 Dictionary&lt;string, object&gt; 集合
            </summary>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.UpdateDictImpl.Where(System.String,System.Object)">
            <summary>
            原生sql语法条件，Where("col = @xxx", new { xxx = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">sql语法条件</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:FreeSqlGlobalExtensions.DeleteDictImpl.Where(System.String,System.Object)">
            <summary>
            原生sql语法条件，Where("col = @xxx", new { xxx = 1 })<para></para>
            提示：parms 参数还可以传 Dictionary&lt;string, object&gt;
            </summary>
            <param name="sql">sql语法条件</param>
            <param name="parms">参数</param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.And``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            使用 and 拼接两个 lambda 表达式
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.And``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Boolean,System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            使用 and 拼接两个 lambda 表达式
            </summary>
            <param name="exp1"></param>
            <param name="condition">true 时生效</param>
            <param name="exp2"></param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Or``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            使用 or 拼接两个 lambda 表达式
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Or``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Boolean,System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            使用 or 拼接两个 lambda 表达式
            </summary>
            <param name="exp1"></param>
            <param name="condition">true 时生效</param>
            <param name="exp2"></param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Not``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Boolean)">
            <summary>
            将 lambda 表达式取反
            </summary>
            <param name="exp"></param>
            <param name="condition">true 时生效</param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.And``2(System.Linq.Expressions.Expression{System.Func{``0,``1,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,``1,System.Boolean}})">
            <summary>
            使用 and 拼接两个 lambda 表达式
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.And``2(System.Linq.Expressions.Expression{System.Func{``0,``1,System.Boolean}},System.Boolean,System.Linq.Expressions.Expression{System.Func{``0,``1,System.Boolean}})">
            <summary>
            使用 and 拼接两个 lambda 表达式
            </summary>
            <param name="exp1"></param>
            <param name="condition">true 时生效</param>
            <param name="exp2"></param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Or``2(System.Linq.Expressions.Expression{System.Func{``0,``1,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,``1,System.Boolean}})">
            <summary>
            使用 or 拼接两个 lambda 表达式
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Or``2(System.Linq.Expressions.Expression{System.Func{``0,``1,System.Boolean}},System.Boolean,System.Linq.Expressions.Expression{System.Func{``0,``1,System.Boolean}})">
            <summary>
            使用 or 拼接两个 lambda 表达式
            </summary>
            <param name="exp1"></param>
            <param name="condition">true 时生效</param>
            <param name="exp2"></param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Not``2(System.Linq.Expressions.Expression{System.Func{``0,``1,System.Boolean}},System.Boolean)">
            <summary>
            将 lambda 表达式取反
            </summary>
            <param name="exp"></param>
            <param name="condition">true 时生效</param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.And``3(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2,System.Boolean}})">
            <summary>
            使用 and 拼接两个 lambda 表达式
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.And``3(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,System.Boolean}},System.Boolean,System.Linq.Expressions.Expression{System.Func{``0,``1,``2,System.Boolean}})">
            <summary>
            使用 and 拼接两个 lambda 表达式
            </summary>
            <param name="exp1"></param>
            <param name="condition">true 时生效</param>
            <param name="exp2"></param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Or``3(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2,System.Boolean}})">
            <summary>
            使用 or 拼接两个 lambda 表达式
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Or``3(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,System.Boolean}},System.Boolean,System.Linq.Expressions.Expression{System.Func{``0,``1,``2,System.Boolean}})">
            <summary>
            使用 or 拼接两个 lambda 表达式
            </summary>
            <param name="exp1"></param>
            <param name="condition">true 时生效</param>
            <param name="exp2"></param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Not``3(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,System.Boolean}},System.Boolean)">
            <summary>
            将 lambda 表达式取反
            </summary>
            <param name="exp"></param>
            <param name="condition">true 时生效</param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.And``4(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,System.Boolean}})">
            <summary>
            使用 and 拼接两个 lambda 表达式
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.And``4(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,System.Boolean}},System.Boolean,System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,System.Boolean}})">
            <summary>
            使用 and 拼接两个 lambda 表达式
            </summary>
            <param name="exp1"></param>
            <param name="condition">true 时生效</param>
            <param name="exp2"></param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Or``4(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,System.Boolean}})">
            <summary>
            使用 or 拼接两个 lambda 表达式
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Or``4(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,System.Boolean}},System.Boolean,System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,System.Boolean}})">
            <summary>
            使用 or 拼接两个 lambda 表达式
            </summary>
            <param name="exp1"></param>
            <param name="condition">true 时生效</param>
            <param name="exp2"></param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Not``4(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,System.Boolean}},System.Boolean)">
            <summary>
            将 lambda 表达式取反
            </summary>
            <param name="exp"></param>
            <param name="condition">true 时生效</param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.And``5(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,System.Boolean}})">
            <summary>
            使用 and 拼接两个 lambda 表达式
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.And``5(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,System.Boolean}},System.Boolean,System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,System.Boolean}})">
            <summary>
            使用 and 拼接两个 lambda 表达式
            </summary>
            <param name="exp1"></param>
            <param name="condition">true 时生效</param>
            <param name="exp2"></param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Or``5(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,System.Boolean}})">
            <summary>
            使用 or 拼接两个 lambda 表达式
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Or``5(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,System.Boolean}},System.Boolean,System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,System.Boolean}})">
            <summary>
            使用 or 拼接两个 lambda 表达式
            </summary>
            <param name="exp1"></param>
            <param name="condition">true 时生效</param>
            <param name="exp2"></param>
            <returns></returns>
        </member>
        <member name="M:System.Linq.Expressions.LambadaExpressionExtensions.Not``5(System.Linq.Expressions.Expression{System.Func{``0,``1,``2,``3,``4,System.Boolean}},System.Boolean)">
            <summary>
            将 lambda 表达式取反
            </summary>
            <param name="exp"></param>
            <param name="condition">true 时生效</param>
            <returns></returns>
        </member>
        <member name="M:FreeUtil.NewMongodbId">
            <summary>
            生成类似Mongodb的ObjectId有序、不重复Guid
            </summary>
            <returns></returns>
        </member>
        <member name="M:IFreeSql.Insert``1">
            <summary>
            插入数据
            </summary>
            <typeparam name="T1"></typeparam>
            <returns></returns>
        </member>
        <member name="M:IFreeSql.Insert``1(``0)">
            <summary>
            插入数据，传入实体
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:IFreeSql.Insert``1(``0[])">
            <summary>
            插入数据，传入实体数组
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:IFreeSql.Insert``1(System.Collections.Generic.List{``0})">
            <summary>
            插入数据，传入实体集合
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:IFreeSql.Insert``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            插入数据，传入实体集合
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:IFreeSql.InsertOrUpdate``1">
            <summary>
            插入或更新数据，此功能依赖数据库特性（低版本可能不支持），参考如下：<para></para>
            MySql 5.6+: on duplicate key update<para></para>
            PostgreSQL 9.4+: on conflict do update<para></para>
            SqlServer 2008+: merge into<para></para>
            Oracle 11+: merge into<para></para>
            Sqlite: replace into<para></para>
            DuckDB: on conflict do update<para></para>
            达梦: merge into<para></para>
            人大金仓：on conflict do update<para></para>
            神通：merge into<para></para>
            MsAccess：不支持<para></para>
            注意区别：FreeSql.Repository 仓储也有 InsertOrUpdate 方法（不依赖数据库特性）
            </summary>
            <typeparam name="T1"></typeparam>
            <returns></returns>
        </member>
        <member name="M:IFreeSql.Update``1">
            <summary>
            修改数据
            </summary>
            <typeparam name="T1"></typeparam>
            <returns></returns>
        </member>
        <member name="M:IFreeSql.Update``1(System.Object)">
            <summary>
            修改数据，传入动态条件，如：主键值 | new[]{主键值1,主键值2} | TEntity1 | new[]{TEntity1,TEntity2} | new{id=1}
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="dywhere">主键值、主键值集合、实体、实体集合、匿名对象、匿名对象集合</param>
            <returns></returns>
        </member>
        <member name="M:IFreeSql.Select``1">
            <summary>
            查询数据
            </summary>
            <typeparam name="T1"></typeparam>
            <returns></returns>
        </member>
        <member name="M:IFreeSql.Select``1(System.Object)">
            <summary>
            查询数据，传入动态条件，如：主键值 | new[]{主键值1,主键值2} | TEntity1 | new[]{TEntity1,TEntity2} | new{id=1}
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="dywhere">主键值、主键值集合、实体、实体集合、匿名对象、匿名对象集合</param>
            <returns></returns>
        </member>
        <member name="M:IFreeSql.Delete``1">
            <summary>
            删除数据
            </summary>
            <typeparam name="T1"></typeparam>
            <returns></returns>
        </member>
        <member name="M:IFreeSql.Delete``1(System.Object)">
            <summary>
            删除数据，传入动态条件，如：主键值 | new[]{主键值1,主键值2} | TEntity1 | new[]{TEntity1,TEntity2} | new{id=1}
            </summary>
            <typeparam name="T1"></typeparam>
            <param name="dywhere">主键值、主键值集合、实体、实体集合、匿名对象、匿名对象集合</param>
            <returns></returns>
        </member>
        <member name="M:IFreeSql.Transaction(System.Action)">
            <summary>
            开启事务（不支持异步）<para></para>
            v1.5.0 关闭了线程事务超时自动提交的机制
            </summary>
            <param name="handler">事务体 () => {}</param>
        </member>
        <member name="M:IFreeSql.Transaction(System.Data.IsolationLevel,System.Action)">
            <summary>
            开启事务（不支持异步）<para></para>
            v1.5.0 关闭了线程事务超时自动提交的机制
            </summary>
            <param name="isolationLevel"></param>
            <param name="handler">事务体 () => {}</param>
        </member>
        <member name="P:IFreeSql.Ado">
            <summary>
            数据库访问对象
            </summary>
        </member>
        <member name="P:IFreeSql.Aop">
            <summary>
            所有拦截方法都在这里
            </summary>
        </member>
        <member name="P:IFreeSql.CodeFirst">
            <summary>
            CodeFirst 模式开发相关方法
            </summary>
        </member>
        <member name="P:IFreeSql.DbFirst">
            <summary>
            DbFirst 模式开发相关方法
            </summary>
        </member>
        <member name="P:IFreeSql.GlobalFilter">
            <summary>
            全局过滤设置，可默认附加为 Select/Update/Delete 条件
            </summary>
        </member>
    </members>
</doc>
