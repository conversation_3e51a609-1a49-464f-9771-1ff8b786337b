﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net40;net45;netstandard1.5</TargetFrameworks>
    <EnableNETAnalyzers>false</EnableNETAnalyzers>
    <EnforceCodeStyleInBuild>false</EnforceCodeStyleInBuild>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <AssemblyName>RJCP.SerialPortStream</AssemblyName>
    <Product>RJCP.SerialPortStream</Product>
    <RootNamespace>RJCP.IO.Ports</RootNamespace>
    <Configurations>Debug;Release;Signed_Release</Configurations>

    <AssemblyTitle>SerialPortStream</AssemblyTitle>
    <Authors>Jason <PERSON>l</Authors>
    <Copyright>(C) <PERSON>, 2012-2024</Copyright>
    <PackageId>SerialPortStream</PackageId>
    <PackageLicenseExpression>MS-PL</PackageLicenseExpression>
    <PackageRequireLicenseAcceptance>true</PackageRequireLicenseAcceptance>
    <PackageProjectUrl>https://github.com/jcurl/serialportstream</PackageProjectUrl>
    <Description>An independent implementation of System.IO.Ports.SerialPort and SerialStream for better reliability and maintainability. Version 3.x and later will be under the package name RJCP.SerialPortStream.</Description>
    <Version>2.4.2</Version>

    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Debug'">
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Release'">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Signed_Release'">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <AssemblyOriginatorKeyFile>rjcp_serialportstream.snk</AssemblyOriginatorKeyFile>
    <SignAssembly>true</SignAssembly>
    <DelaySign>false</DelaySign>
  </PropertyGroup>

  <ItemGroup Condition="$(TargetFramework.StartsWith('net4'))">
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Management" />
    <Reference Include="Microsoft.CSharp" />
    <PackageReference Include="Mono.Posix" Version="4.0.0.0">
      <PrivateAssets>all</PrivateAssets>
      <ExcludeAssets>runtime</ExcludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup Condition=" '$(TargetFramework)' == 'netstandard1.5' ">
    <PackageReference Include="System.Threading.Overlapped" Version="4.3.0" />
    <PackageReference Include="System.Threading.Thread" Version="4.3.0" />
    <PackageReference Include="System.Collections.Specialized" Version="4.3.0" />
    <PackageReference Include="System.Diagnostics.FileVersionInfo" Version="4.3.0" />
    <PackageReference Include="System.Diagnostics.TraceSource" Version="4.3.0" />
    <PackageReference Include="System.Threading.ThreadPool" Version="4.3.0" />
    <PackageReference Include="Microsoft.Win32.Registry" Version="4.3.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="1.1.2" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="Datastructures\CircularBuffer.cs" />
    <Compile Include="Datastructures\ReusableList.cs" />
    <Compile Include="Datastructures\TimerExpiry.cs" />
    <Compile Include="GlobalSuppressions.cs" />
    <Compile Include="HandShake.cs" />
    <Compile Include="InternalApplicationException.cs" />
    <Compile Include="ISerialPortStream.cs" />
    <Compile Include="LocalAsyncResult.cs" />
    <Compile Include="Native\INativeSerial.cs" />
    <Compile Include="Native\ISerialBufferSerialData.cs" />
    <Compile Include="Native\ISerialBufferStreamData.cs" />
    <Compile Include="Native\Platform.cs" />
    <Compile Include="Native\ReadToCache.cs" />
    <Compile Include="Native\SerialBuffer.cs" />
    <Compile Include="Native\Unix\INativeSerialDll.cs" />
    <Compile Include="Native\Unix\SafeNativeMethods.cs" />
    <Compile Include="Native\Unix\SafeSerialHandle.cs" />
    <Compile Include="Native\Unix\SerialUnix.cs" />
    <Compile Include="Native\Unix\SerialReadWriteEvent.cs" />
    <Compile Include="Native\Unix\SysErrNo.cs" />
    <Compile Include="Native\Unix\WaitForModemEvent.cs" />
    <Compile Include="Native\Unix\UnsafeNativeMethods.cs" />
    <Compile Include="Native\UnixNativeSerial.cs" />
    <Compile Include="Native\Windows\CfgMgr32.cs" />
    <Compile Include="Native\Windows\CfgMgr32.Types.cs" />
    <Compile Include="Native\Windows\CommErrorEventArgs.cs" />
    <Compile Include="Native\Windows\CommEventArgs.cs" />
    <Compile Include="Native\Windows\CommOverlappedIo.cs" />
    <Compile Include="Native\Windows\CommState.cs" />
    <Compile Include="Native\Windows\CommProperties.cs" />
    <Compile Include="Native\Windows\CommModemStatus.cs" />
    <Compile Include="Native\Windows\DtrControl.cs" />
    <Compile Include="Native\Windows\Kernel32.Types.cs" />
    <Compile Include="Native\Windows\Marshalling.cs" />
    <Compile Include="Native\Windows\RtsControl.cs" />
    <Compile Include="Native\Windows\Kernel32.cs" />
    <Compile Include="Native\Windows\WinError.cs" />
    <Compile Include="Native\WinNativeSerial.cs" />
    <Compile Include="Parity.cs" />
    <Compile Include="PortDescription.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SerialData.cs" />
    <Compile Include="SerialDataEventArgs.cs" />
    <Compile Include="SerialError.cs" />
    <Compile Include="SerialErrorReceivedEventArgs.cs" />
    <Compile Include="SerialPinChange.cs" />
    <Compile Include="SerialPinChangedEventArgs.cs" />
    <Compile Include="SerialPortStream.cs" />
    <Compile Include="StopBits.cs" />
    <None Include="System\EntryPointNotFoundException.cs" />
    <None Include="Trace\LineSplitter.cs" />
    <Compile Include="Trace\Log.cs" />
    <None Include="Trace\LoggerTraceListener.cs" />
    <Compile Include="Trace\LogSource.cs" />
    <None Include="Trace\LogSourceFactory.cs" />
  </ItemGroup>

  <ItemGroup Condition=" '$(TargetFramework)' == 'netstandard1.5' ">
    <Compile Include="System\EntryPointNotFoundException.cs" />
    <Compile Include="Trace\LineSplitter.cs" />
    <Compile Include="Trace\LoggerTraceListener.cs" />
    <Compile Include="Trace\LogSourceFactory.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Include="rjcp_serialportstream.snk" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\LICENSE.md" Pack="true" PackagePath="\" />
    <None Include="..\README.md" Pack="true" PackagePath="\" />
  </ItemGroup>
</Project>
