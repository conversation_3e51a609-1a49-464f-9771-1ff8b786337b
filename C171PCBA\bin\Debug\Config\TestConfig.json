{"Project": "C171_PCBA", "Version": "1.0.0.0", "UpdateTime": "2025-07-28 10:00:00", "Data": [{"Name": "等待设备连接", "CmdStr": "", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "fix_connect", "WaitTime": 1000, "WaitCount": 30, "Retry": 1, "SleepTime": 0, "Remark": "", "Params": [{"Name": "等待设备连接", "IsEnabled": true, "RegexPattern": "fix_connect", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "fix_connect", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "检测接地电压Input22", "CmdStr": "1A A1 40 00 00", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V22", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "检测接地电压Input22", "IsEnabled": true, "RegexPattern": "Get AD_V22.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V22", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 50.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开POWER_OUT2", "CmdStr": "5A A5 03 5A A5", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OPEN_POWER_OUT1", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开POWER_OUT2", "IsEnabled": true, "RegexPattern": "OPEN_POWER_OUT1", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "OPEN_POWER_OUT1", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "拉低OUTPUT1", "CmdStr": "5A A5 01 5A A5", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OFF_OUTPUT1", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "拉低OUTPUT1", "IsEnabled": true, "RegexPattern": "OFF_OUTPUT1", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "OFF_OUTPUT1", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "进入产测模式", "CmdStr": "<SPBSJ*N:91325280001>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "进入产测模式关键字", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "进入产测模式", "IsEnabled": true, "RegexPattern": "进入产测模式关键字", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "进入产测模式关键字", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "参数写入", "CmdStr": "<SPBSJ*N:91325280001>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "参数写入", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "参数校验", "CmdStr": "<CKBSJ>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "参数校验", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "版本校验", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "软件版本", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "硬件版本", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "VSYS电压检测INPUT1", "CmdStr": "1A A1 00 00 02", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V1", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "VSYS电压检测INPUT1", "IsEnabled": true, "RegexPattern": "Get AD_V1.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V1", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 4100.0, "MaxValue": 4300.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "+3V3电压检测INPUT2", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "+3V3电压检测INPUT2", "IsEnabled": true, "RegexPattern": "Get AD_V2.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 3200.0, "MaxValue": 3400.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "+5V电压检测INPUT4", "CmdStr": "1A A1 00 00 10", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V4", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "+3V3电压检测INPUT2", "IsEnabled": true, "RegexPattern": "Get AD_V4.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V4", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 4900.0, "MaxValue": 5100.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "PWR_DET检测(自检)", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "PWR_DET检测(自检)", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "硬件版本", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开NTC拉低OUTPUT3", "CmdStr": "3A A3 03 00 A3", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OFF_OUTPUT3", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开NTC拉低OUTPUT3", "IsEnabled": true, "RegexPattern": "OFF_OUTPUT3", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "OFF_OUTPUT3", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开电池(大量程)", "CmdStr": "6A A6 01 6A A6", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OPEN_VBAT1", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开电池(大量程)", "IsEnabled": true, "RegexPattern": "OPEN_VBAT1", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "OPEN_VBAT1", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开充电", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开充电", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "硬件版本", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "充电电流(1)", "CmdStr": "AT+AVG?", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 8, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "充电电流(1)", "IsEnabled": true, "RegexPattern": "ACK.CURRENT.*uA", "CutTextType": 3, "CutTextFlag1": "ACK+CURRENT:", "CutTextFlag2": "uA", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 200.0, "Unit": "uA", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "断开NTC拉高OUTPUT3", "CmdStr": "3A A3 03 01 A3", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "ON_OUTPUT3", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "断开NTC拉高OUTPUT3", "IsEnabled": true, "RegexPattern": "ON_OUTPUT3", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "ON_OUTPUT3", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "充电电流(2)", "CmdStr": "AT+AVG?", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 8, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "充电电流(2)", "IsEnabled": true, "RegexPattern": "ACK.CURRENT.*uA", "CutTextType": 3, "CutTextFlag1": "ACK+CURRENT:", "CutTextFlag2": "uA", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 200.0, "Unit": "uA", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开NTC拉低OUTPUT3(2)", "CmdStr": "3A A3 03 00 A3", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OFF_OUTPUT3", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开NTC拉低OUTPUT3(2)", "IsEnabled": true, "RegexPattern": "OFF_OUTPUT3", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "OFF_OUTPUT3", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "关闭充电", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "关闭充电", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "硬件版本", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "断开电池", "CmdStr": "6A A6 02 6A A6", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "CLOSE_VBAT1", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "断开电池", "IsEnabled": true, "RegexPattern": "CLOSE_VBAT1", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "CLOSE_VBAT1", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开红灯", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开红灯", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "读取光敏1(INPUT10)(1)", "CmdStr": "1A A1 00 00 02", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V10", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "读取光敏1(INPUT10)(1)", "IsEnabled": true, "RegexPattern": "Get AD_V10.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V10", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 4100.0, "MaxValue": 4300.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "读取光敏1(INPUT11)", "CmdStr": "1A A1 00 00 02", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V11", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "读取光敏1(INPUT11)", "IsEnabled": true, "RegexPattern": "Get AD_V11.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V11", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 4100.0, "MaxValue": 4300.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "关闭红灯", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "关闭红灯", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开绿灯", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开绿灯", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "读取光敏1(INPUT10)(2)", "CmdStr": "1A A1 00 00 02", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V10", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "读取光敏1(INPUT10)(2)", "IsEnabled": true, "RegexPattern": "Get AD_V10.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V10", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 4100.0, "MaxValue": 4300.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "读取光敏1(INPUT11)(2)", "CmdStr": "1A A1 00 00 02", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V11", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "读取光敏1(INPUT11)(2)", "IsEnabled": true, "RegexPattern": "Get AD_V11.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V11", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 4100.0, "MaxValue": 4300.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "关闭绿灯", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "关闭绿灯", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开蓝灯", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开蓝灯", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "读取光敏1(INPUT10)(3)", "CmdStr": "1A A1 00 00 02", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V10", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "读取光敏1(INPUT10)(3)", "IsEnabled": true, "RegexPattern": "Get AD_V10.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V10", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 4100.0, "MaxValue": 4300.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "读取光敏1(INPUT11)(2)", "CmdStr": "1A A1 00 00 02", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V11", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "读取光敏1(INPUT11)(2)", "IsEnabled": true, "RegexPattern": "Get AD_V11.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V11", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 4100.0, "MaxValue": 4300.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "关闭蓝灯", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "关闭蓝灯", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开蜂鸣器", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开蜂鸣器", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "获取声响电压(1)", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "获取声响电压(1)", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": "测试板1秒内连续读取INPUT23 10000次电压值,取3个最大值"}]}, {"Name": "人工判断声响", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "人工判断声响", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "关闭蜂鸣器", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "关闭蜂鸣器", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "获取声响电压(2)", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "获取声响电压(2)", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": "测试板读取INPUT23电压值"}]}, {"Name": "六轴测试", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "六轴测试", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "震动测试", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "震动测试", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "CAN测试", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "CAN测试", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开5V_HALL", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开5V_HALL", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "+5V_HALL电压检测(INPUT5)(1)", "CmdStr": "1A A1 00 00 20", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V1", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "+5V_HALL电压检测(INPUT5)(1)", "IsEnabled": true, "RegexPattern": "Get AD_V1.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V1", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 4900.0, "MaxValue": 5100.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "HALL_DET测试", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "HALL_DET测试", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "关闭5V_HALL", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "关闭5V_HALL", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "+5V_HALL电压检测(INPUT5)(2)", "CmdStr": "1A A1 00 00 20", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V1", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "+5V_HALL电压检测(INPUT5)(2)", "IsEnabled": true, "RegexPattern": "Get AD_V1.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V1", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 0.0, "MaxValue": 50.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开+5V_OUT", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开+5V_OUT", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "+5V_OUT电压检测(INPUT6)(1)", "CmdStr": "1A A1 00 00 40", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V6", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "+5V_OUT电压检测(INPUT6)(1)", "IsEnabled": true, "RegexPattern": "Get AD_V6.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V6", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 4900.0, "MaxValue": 5100.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "力矩信号测试", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "力矩信号测试", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "踏频信号输入高OUTPUT4", "CmdStr": "3A A3 04 01 A3", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "ON_OUTPUT3", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "踏频信号输入高OUTPUT4", "IsEnabled": true, "RegexPattern": "ON_OUTPUT4", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "ON_OUTPUT4", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "踏频信号输入检测高", "CmdStr": "3A A3 04 01 A3", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "踏频信号输入检测", "IsEnabled": true, "RegexPattern": "", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "踏频信号输入低OUTPUT4", "CmdStr": "3A A3 04 00 A3", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "ON_OUTPUT3", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "踏频信号输入低OUTPUT4", "IsEnabled": true, "RegexPattern": "ON_OUTPUT4", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "ON_OUTPUT4", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "踏频信号输入检测低", "CmdStr": "3A A3 04 01 A3", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "踏频信号输入检测低", "IsEnabled": true, "RegexPattern": "", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "关闭+5V_OUT", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "关闭+5V_OUT", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "+5V_OUT电压检测(INPUT6)(2)", "CmdStr": "1A A1 00 00 40", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V6", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "+5V_OUT电压检测(INPUT6)(2)", "IsEnabled": true, "RegexPattern": "Get AD_V6.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V6", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 4900.0, "MaxValue": 5100.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "按键未按下检测", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "按键未按下检测", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "按键按下检测", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "按键按下检测", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "获取对应MAC的蓝牙RSSI(MCU)", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "获取对应MAC的蓝牙RSSI(MCU)", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "获取涂鸦模组蓝牙MAC", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "获取涂鸦模组蓝牙MAC", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "获取对应MAC的蓝牙RSSI(涂鸦)", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "获取对应MAC的蓝牙RSSI(涂鸦)", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开TypeC供电拉低OUTPUT2", "CmdStr": "3A A3 02 00 A3", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OFF_OUTPUT2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开TypeC供电拉低OUTPUT2", "IsEnabled": true, "RegexPattern": "OFF_OUTPUT2", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "OFF_OUTPUT2", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "断开主电供电拉高OUTPUT1", "CmdStr": "3A A3 04 01 A3", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "ON_OUTPUT3", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "断开主电供电拉高OUTPUT1", "IsEnabled": true, "RegexPattern": "ON_OUTPUT4", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "ON_OUTPUT4", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "PWR_DET检测(自检2)", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "PWR_DET检测(自检2)", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "硬件版本", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "VSYS电压检测Input1(1)", "CmdStr": "1A A1 40 00 00", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V22", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "VSYS电压检测Input1(1)", "IsEnabled": true, "RegexPattern": "Get AD_V22.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V22", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 50.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开电池(大量程)", "CmdStr": "6A A6 01 6A A6", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OPEN_VBAT1", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开电池(大量程)", "IsEnabled": true, "RegexPattern": "OPEN_VBAT1", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "OPEN_VBAT1", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开充电", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开充电", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "硬件版本", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "充电电流(1)", "CmdStr": "AT+AVG?", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 8, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "充电电流(1)", "IsEnabled": true, "RegexPattern": "ACK.CURRENT.*uA", "CutTextType": 3, "CutTextFlag1": "ACK+CURRENT:", "CutTextFlag2": "uA", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 200.0, "Unit": "uA", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "关闭充电", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "关闭充电", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "硬件版本", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "关闭TypeC供电拉高OUTPUT2", "CmdStr": "3A A3 02 01 A3", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "ON_OUTPUT2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "关闭TypeC供电拉高OUTPUT2", "IsEnabled": true, "RegexPattern": "ON_OUTPUT2", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "ON_OUTPUT2", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "VSYS电压检测Input1(2)", "CmdStr": "1A A1 40 00 00", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V22", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "VSYS电压检测Input1(2)", "IsEnabled": true, "RegexPattern": "Get AD_V22.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V22", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 50.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "电池电压测试", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "电池电压测试", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "进入休眠模式", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "进入休眠模式", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "+5V电压测试(INPUT4)", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "+5V电压测试(INPUT4)", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "休眠功耗测试", "CmdStr": "AT+AVG?", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 8, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "休眠功耗测试", "IsEnabled": true, "RegexPattern": "ACK.CURRENT.*uA", "CutTextType": 3, "CutTextFlag1": "ACK+CURRENT:", "CutTextFlag2": "uA", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 200.0, "Unit": "uA", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "检测TP11电压3V3-Input2", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "检测TP11电压3V3-Input2", "IsEnabled": true, "RegexPattern": "Get AD_V2.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 3100.0, "MaxValue": 3500.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "参数写入", "CmdStr": "<SPBSJ*N:91325280001>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "参数写入", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "版本查询", "CmdStr": "<CKVER>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "UBoot版本", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "硬件版本", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "固件版本", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "测试LB-电压输出TP12-Input3", "CmdStr": "1A A1 00 00 08", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V3", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "测试LB-电压输出", "IsEnabled": true, "RegexPattern": "Get AD_V3.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V3", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 2400.0, "MaxValue": 2600.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "一线通测试", "CmdStr": "<CKBSJ>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "待测试再确定返回值是什么", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "一线通测试", "IsEnabled": true, "RegexPattern": "待测试再确定返回值是什么", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "待测试再确定返回值是什么", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "ACC测试", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "ACC测试", "IsEnabled": true, "RegexPattern": "Get AD_V2.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 3100.0, "MaxValue": 3500.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "喇叭测试", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "喇叭测试", "IsEnabled": true, "RegexPattern": "Get AD_V2.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 3100.0, "MaxValue": 3500.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "LBKEY按键测试", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "LBKEY按键测试", "IsEnabled": true, "RegexPattern": "Get AD_V2.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 3100.0, "MaxValue": 3500.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "点亮全部灯珠", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "点亮全部灯珠", "IsEnabled": true, "RegexPattern": "Get AD_V2.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 3100.0, "MaxValue": 3500.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "待机功耗(全亮灯)", "CmdStr": "7A A7 01 7A A7", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 8, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "待机功耗(全亮灯)", "IsEnabled": true, "RegexPattern": "ACK.CURRENT.*uA", "CutTextType": 3, "CutTextFlag1": "ACK+CURRENT:", "CutTextFlag2": "uA", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 200.0, "Unit": "uA", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "关闭全部灯珠", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Get AD_V2", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "关闭全部灯珠", "IsEnabled": true, "RegexPattern": "Get AD_V2.*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 3100.0, "MaxValue": 3500.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "待机功耗(不亮灯)", "CmdStr": "7A A7 01 7A A7", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 8, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "待机功耗(不亮灯)", "IsEnabled": true, "RegexPattern": "ACK.CURRENT.*uA", "CutTextType": 3, "CutTextFlag1": "ACK+CURRENT:", "CutTextFlag2": "uA", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 200.0, "Unit": "uA", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "休眠功耗", "CmdStr": "7A A7 01 7A A7", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 8, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "休眠功耗", "IsEnabled": true, "RegexPattern": "ACK.CURRENT.*uA", "CutTextType": 3, "CutTextFlag1": "ACK+CURRENT:", "CutTextFlag2": "uA", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 200.0, "Unit": "uA", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "MES过站", "CmdStr": "", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "", "WaitTime": 250, "WaitCount": 4, "Retry": 1, "SleepTime": 0, "Remark": "", "Params": [{"Name": "MES过站", "IsEnabled": false, "RegexPattern": "^[A-Za-z]+", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "True", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 10.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}]}