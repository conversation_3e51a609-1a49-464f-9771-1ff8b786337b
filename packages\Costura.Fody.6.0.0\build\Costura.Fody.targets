<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup Condition="$(CosturaRemoveCopyLocalFilesToPublish) == ''">
    <CosturaRemoveCopyLocalFilesToPublish>true</CosturaRemoveCopyLocalFilesToPublish>
  </PropertyGroup>

  <Target Name="CosturaRemoveAlreadyEmbeddedFilesFromPublish" AfterTargets="ComputeResolvedFilesToPublishList" Condition="$(CosturaRemoveCopyLocalFilesToPublish) == 'true'">
    <ItemGroup>
      <ResolvedFileToPublish Remove="@(FodyRemovedReferenceCopyLocalPaths)" />
    </ItemGroup>
  </Target>

</Project>