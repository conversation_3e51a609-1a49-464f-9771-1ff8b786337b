﻿![SunnyUI.Net](https://camo.githubusercontent.com/fddb15a3839c30fbee3a0ac9635069789288e7e770b62ce31f14149e63743a18/68747470733a2f2f696d616765732e67697465652e636f6d2f75706c6f6164732f696d616765732f323032312f303332342f3231333631355f35343234306261395f3431363732302e706e67 "SunnyUI.png")    

[![star](https://gitee.com/yhuse/SunnyUI/badge/star.svg?theme=gvp)](https://gitee.com/yhuse/SunnyUI/stargazers)
[![fork](https://gitee.com/yhuse/SunnyUI/badge/fork.svg?theme=gvp)](https://gitee.com/yhuse/SunnyUI/members)    
- 帮助文档: [https://gitee.com/yhuse/SunnyUI/wikis/pages](https://gitee.com/yhuse/SunnyUI/wikis/pages) 
- 更新日志: [https://gitee.com/yhuse/SunnyUI/wikis/更新日志](https://gitee.com/yhuse/SunnyUI/wikis/%E6%9B%B4%E6%96%B0%E6%97%A5%E5%BF%97)
- Gitee: [https://gitee.com/yhuse/SunnyUI](https://gitee.com/yhuse/SunnyUI)    
- GitHub: [https://github.com/yhuse/SunnyUI](https://github.com/yhuse/SunnyUI)    
- Nuget: [https://www.nuget.org/packages/SunnyUI/](https://www.nuget.org/packages/SunnyUI/)    
- Blog: [https://www.cnblogs.com/yhuse](https://www.cnblogs.com/yhuse)    
- 因为评论没有查找，不利于解决问题，故关闭项目评论功能。如果是问题或者建议，请按照Issues模版添加Issue。    
- 添加Issue: [https://gitee.com/yhuse/SunnyUI/issues/new](https://gitee.com/yhuse/SunnyUI/issues/new)    
- V3.6.8+Demo编译可执行文件: [https://gitee.com/yhuse/SunnyUI.Demo](https://gitee.com/yhuse/SunnyUI.Demo)
    
欢迎交流，QQ群： 56829229  (SunnyUI技术交流群)，请给源码项目点个Star吧！！！  
**个人学习交流免费，商业应用需要授权**，联系QQ：**17612584** 咨询授权事宜。  

#### 支持开源
- 希望SunnyUI对您有用，您的支持也是SunnyUI开源的动力，SunnyUI有您更精彩！
![感谢您的支持](https://camo.githubusercontent.com/0df607315cfda18b5afc5d6173400eb2a15b51ccffc81efb57c7cfe9824c8b6e/68747470733a2f2f696d616765732e67697465652e636f6d2f75706c6f6164732f696d616765732f323032312f303430392f3230313535385f39613039393361335f3431363732302e706e67 "SunnyUISupport.png")
- 感谢老板打赏，来杯咖啡提提神，写代码更有劲头！~

#### 感谢码云
![GVP](https://images.gitee.com/uploads/images/2021/0526/214138_85647268_416720.png "QQ图片20210526213958.png")  
    
#### 特别声明
SunnyUI.Net项目已加入[dotNET China](https://gitee.com/dotnetchina) 组织。<br/>
![dotnetchina](https://images.gitee.com/uploads/images/2021/0324/120117_2da9922c_416720.png "132645_21007ea0_974299.png")

#### 软件介绍
- SunnyUI.NET 是基于.Net Framework4.0+、.Net8、.Net9 框架的 C# WinForm 开源控件库、工具类库、扩展类库、多页面开发框架。    
- 源码编译环境：    
  1. **VS2022**，安装.NetFramework4.0目标包的方法见：https://www.cnblogs.com/yhuse/p/15536393.html    
  2. 编译源码，.Net9需要VS2022 17.14+版本，或者修改SunnyUI.csproj文件的TargetFrameworks属性以适应VS环境    
- 动态库应用环境： **VS2010**及以上均可，支持.Net Framework4.0+、.Net8、.Net9    
  1. 推荐通过Nuget安装：Install-Package SunnyUI，或者通过Nuget搜索SunnyUI安装。    
  2. 不支持（.Net Framework 4 **Client Profile**）。     
    
#### 软件框架
![思维导图](https://images.gitee.com/uploads/images/2020/0627/210016_f3203a8b_416720.png "0.png")

1、开源控件库  

  - 基于.Net Framework4.0，原生控件开发，参考 Element主题风格，包含 按钮、编辑框、下拉框、数据表格、工控仪表、统计图表在内的常用控件超过  **70** 个，满足常规开发需求，每个控件都精雕细琢，注重细节；  
  - 包含 Element 风格主题 11 个，其他主题 6 个，可通过多彩主题模式自定义主题。包含主题管理组件 UIStyleManager，可自由切换主题。  

![输入图片说明](https://foruda.gitee.com/images/1695452615395997083/00202d42_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452642851863238/aaeec26f_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452680081274144/3c1e7f98_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452700873038668/063ed9c0_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452736023225936/3390f8f2_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452755229762213/ca32e657_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452775585329155/0dbba2b2_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452790242801454/adfb6473_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452812586687608/90d4f3eb_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452847110834271/9be91c47_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452877418500901/2c591614_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452892176132380/6c05f728_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452907304141254/2dd0aa1b_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452922677145039/3e8f40d1_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452941707002701/0706c309_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452962527727515/528579ba_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695452975298844079/ec3b9fce_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695453017275109921/7560fffb_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695453028239627243/e3037142_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695453068134019308/8a62c6ca_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695453092842557213/2a930ecc_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695453118713578423/4bb3fa94_416720.png "屏幕截图")
![输入图片说明](https://foruda.gitee.com/images/1695453138393750291/9ec560fd_416720.png "屏幕截图")

2、工具库

  - 收集整理开发过程中经常用到的工具类库。

3、扩展库

  - 收集整理开发过程中经常用到的扩展类库。

4、多页面框架

  - 参考Element，包括7种常用框架风格，只需几行简单的代码即可创建多页面程序，其支撑组件包括UIForm，UIPage，UIFrame，集合常用控件库即可快速开发WinForm应用程序。
![输入图片说明](https://foruda.gitee.com/images/1695453189247127628/1adcd0d0_416720.png "屏幕截图")

#### 项目说明
- 个人学习交流免费，商业应用需要授权。个人承接外包项目、个人开发商业项目等，公司内部使用、开发项目等均属于商业应用范畴。联系QQ：**17612584** 咨询授权事宜。      
- 禁止使用SunnyUI控件库开发违法应用，或使用从事其他非法目的违法犯罪行为。因此产生的法律责任与SunnyUI无关。     