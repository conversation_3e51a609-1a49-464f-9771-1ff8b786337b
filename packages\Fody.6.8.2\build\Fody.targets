﻿<Project>
  <PropertyGroup>
    <ProjectWeaverXml Condition="$(ProjectWeaverXml) == ''">$(ProjectDir)FodyWeavers.xml</ProjectWeaverXml>
    <FodyPath Condition="$(FodyPath) == ''">$(MSBuildThisFileDirectory)..\</FodyPath>
    <FodyAssemblyDirectory Condition="$(MSBuildRuntimeType) == 'Core'">$(FodyPath)netstandardtask</FodyAssemblyDirectory>
    <FodyAssemblyDirectory Condition="$(MSBuildRuntimeType) != 'Core'">$(FodyPath)netclassictask</FodyAssemblyDirectory>
    <FodyAssembly Condition="$(FodyAssembly) == ''">$(FodyAssemblyDirectory)\Fody.dll</FodyAssembly>
    <DefaultItemExcludes>$(DefaultItemExcludes);FodyWeavers.xsd</DefaultItemExcludes>
    <FodyGenerateXsd Condition="$(FodyGenerateXsd) == ''">true</FodyGenerateXsd>
    <MsBuildMajorVersion>15</MsBuildMajorVersion>
    <MsBuildMajorVersion Condition="'$(MSBuildVersion)' != ''">$([System.Version]::Parse($(MSBuildVersion)).Major)</MsBuildMajorVersion>
    <FodyAfterTargets Condition="($(MsBuildMajorVersion) &lt; 17) AND '$(FodyAfterTargets)'==''">AfterCompile</FodyAfterTargets>
    <TargetsTriggeredByCompilation Condition="'$(FodyAfterTargets)'==''">$(TargetsTriggeredByCompilation);FodyTarget</TargetsTriggeredByCompilation>
    <DisableFody Condition='$(UseWPF)==true AND $(MSBuildProjectName.EndsWith("_wpftmp"))'>true</DisableFody>
    <FodyTreatWarningsAsErrors Condition="$(FodyTreatWarningsAsErrors) == ''">$(TreatWarningsAsErrors)</FodyTreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup Condition="Exists($(ProjectWeaverXml))">
    <UpToDateCheckInput Include="$(ProjectWeaverXml)" />
    <CustomAdditionalCompileInputs Include="$(ProjectWeaverXml)" />
  </ItemGroup>

  <!-- Support for NCrunch -->
  <ItemGroup Condition="'$(NCrunch)' == '1' and '$(TargetFramework)' == '' and '$(TargetFrameworks)' == ''">
    <None Include="$(FodyAssemblyDirectory)\*.*" />
    <None Include="@(WeaverFiles)" />
  </ItemGroup>

  <UsingTask TaskName="Fody.WeavingTask" AssemblyFile="$(FodyAssembly)" />
  <UsingTask TaskName="Fody.UpdateCopyLocalTask" AssemblyFile="$(FodyAssembly)" />
  <UsingTask TaskName="Fody.VerifyTask" AssemblyFile="$(FodyAssembly)" />

  <Target
      Name="FodyTarget"
      AfterTargets="$(FodyAfterTargets)"
      Condition="Exists(@(IntermediateAssembly)) And ($(DesignTimeBuild) != true Or $(BuildingForLiveUnitTesting) == true) And $(DisableFody) != true"
      DependsOnTargets="$(FodyDependsOnTargets)"
      Inputs="@(IntermediateAssembly);$(ProjectWeaverXml)"
      Outputs="$(IntermediateOutputPath)$(MSBuildProjectFile).Fody.CopyLocal.cache;$(IntermediateOutputPath)$(MSBuildProjectFile).Fody.RuntimeCopyLocal.cache">

    <Error Condition="($(MsBuildMajorVersion) &lt; 16)"
           Text="Fody is only supported on MSBuild 16 and above. Current version: $(MsBuildMajorVersion)." />

    <Fody.WeavingTask
        AssemblyFile="@(IntermediateAssembly)"
        IntermediateDirectory="$(ProjectDir)$(IntermediateOutputPath)"
        KeyOriginatorFile="$(KeyOriginatorFile)"
        AssemblyOriginatorKeyFile="$(AssemblyOriginatorKeyFile)"
        ProjectDirectory="$(MSBuildProjectDirectory)"
        ProjectFile="$(MSBuildProjectFullPath)"
        SolutionDirectory="$(SolutionDir)"
        References="@(ReferencePath)"
        SignAssembly="$(SignAssembly)"
        DelaySign="$(DelaySign)"
        ReferenceCopyLocalFiles="@(ReferenceCopyLocalPaths)"
        RuntimeCopyLocalFiles="@(RuntimeCopyLocalItems)"
        DefineConstants="$(DefineConstants)"
        DocumentationFile="@(DocFileItem->'%(FullPath)')"
        WeaverFiles="@(WeaverFiles)"
        WeaverConfiguration="$(WeaverConfiguration)"
        PackageReferences="@(PackageReference)"
        NCrunchOriginalSolutionDirectory="$(NCrunchOriginalSolutionDir)"
        IntermediateCopyLocalFilesCache="$(IntermediateOutputPath)$(MSBuildProjectFile).Fody.CopyLocal.cache"
        RuntimeCopyLocalFilesCache="$(IntermediateOutputPath)$(MSBuildProjectFile).Fody.RuntimeCopyLocal.cache"
        GenerateXsd="$(FodyGenerateXsd)"
        TreatWarningsAsErrors="$(FodyTreatWarningsAsErrors)"
      >

      <Output
        TaskParameter="ExecutedWeavers"
        PropertyName="FodyExecutedWeavers" />

    </Fody.WeavingTask>

    <ItemGroup>
      <FileWrites Include="$(IntermediateOutputPath)$(MSBuildProjectFile).Fody.CopyLocal.cache" />
      <FileWrites Include="$(IntermediateOutputPath)$(MSBuildProjectFile).Fody.RuntimeCopyLocal.cache" />
    </ItemGroup>

  </Target>

  <Target
      Name="FodyUpdateReferenceCopyLocalFilesTarget"
      AfterTargets="FodyTarget"
    >

    <Fody.UpdateCopyLocalTask
        CopyLocalFiles="@(ReferenceCopyLocalPaths)"
        IntermediateCopyLocalFilesCache="$(IntermediateOutputPath)$(MSBuildProjectFile).Fody.CopyLocal.cache"
      >

      <Output
        TaskParameter="UpdatedCopyLocalFiles"
        ItemName="FodyUpdatedReferenceCopyLocalPaths" />

    </Fody.UpdateCopyLocalTask>

    <ItemGroup>
      <FodyRemovedReferenceCopyLocalPaths Include="@(ReferenceCopyLocalPaths)" />
      <FodyRemovedReferenceCopyLocalPaths Remove="@(FodyUpdatedReferenceCopyLocalPaths)" />
      <ReferenceCopyLocalPaths Remove="@(ReferenceCopyLocalPaths)" />
      <ReferenceCopyLocalPaths Include="@(FodyUpdatedReferenceCopyLocalPaths)" />
    </ItemGroup>

  </Target>

  <Target
      Name="FodyUpdateRuntimeCopyLocalItemsTarget"
      AfterTargets="FodyTarget"
    >

    <Fody.UpdateCopyLocalTask
        CopyLocalFiles="@(RuntimeCopyLocalItems)"
        IntermediateCopyLocalFilesCache="$(IntermediateOutputPath)$(MSBuildProjectFile).Fody.RuntimeCopyLocal.cache"
      >

      <Output
        TaskParameter="UpdatedCopyLocalFiles"
        ItemName="FodyUpdatedRuntimeCopyLocalItems" />

    </Fody.UpdateCopyLocalTask>

    <ItemGroup>
      <FodyRemovedRuntimeCopyLocalItems Include="@(RuntimeCopyLocalItems)" />
      <FodyRemovedRuntimeCopyLocalItems Remove="@(FodyUpdatedRuntimeCopyLocalItems)" />
      <RuntimeCopyLocalItems Remove="@(RuntimeCopyLocalItems)" />
      <RuntimeCopyLocalItems Include="@(FodyUpdatedRuntimeCopyLocalItems)" />
    </ItemGroup>

  </Target>

  <Target
      Name="FodyVerifyTarget"
      AfterTargets="AfterBuild"
      Condition="'$(NCrunch)' != '1' And $(FodyExecutedWeavers) != '' And $(DisableFody) != true"
      DependsOnTargets="$(FodyVerifyDependsOnTargets)">

    <Fody.VerifyTask
        ProjectDirectory="$(MSBuildProjectDirectory)"
        TargetPath="$(TargetPath)"
        SolutionDirectory="$(SolutionDir)"
        WeaverConfiguration="$(WeaverConfiguration)"
        DefineConstants="$(DefineConstants)"
        NCrunchOriginalSolutionDirectory="$(NCrunchOriginalSolutionDir)"
      />

  </Target>

</Project>
