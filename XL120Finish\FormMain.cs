﻿// 系统基础命名空间

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

// 第三方库引用
using AiHelper; // AI辅助工具库
using BSJ.AI.MES.DynamicInvocation; // MES系统动态调用库
using BSJ.AI.MES.DynamicInvocation.Model; // MES系统数据模型
using BsjHelperV2.Enum; // 博实结辅助库枚举
using BsjHelperV2.Helper; // 博实结辅助库工具类
using BsjHelperV2.Models; // 博实结辅助库数据模型
using BsjHelperV2.UI; // 博实结辅助库UI组件
using XL120Finish.Config; // 项目配置类
using Newtonsoft.Json; // JSON序列化库
using RJCP.IO.Ports; // 高性能串口通信库
using Sunny.UI; // SunnyUI界面库

// 串口相关类型别名，避免命名冲突
using Parity = RJCP.IO.Ports.Parity;
using SerialDataReceivedEventArgs = RJCP.IO.Ports.SerialDataReceivedEventArgs;
using SerialErrorReceivedEventArgs = RJCP.IO.Ports.SerialErrorReceivedEventArgs;
using StopBits = RJCP.IO.Ports.StopBits;
using Timer = System.Windows.Forms.Timer;

namespace XL120Finish
{
    /// <summary>
    /// XL120成品功能测试主窗体类
    /// 继承自SunnyUI的UIForm，提供现代化的界面风格
    /// 主要功能：
    /// 1. 串口通信管理（设备串口和扫码枪串口）
    /// 2. 测试流程控制和状态管理
    /// 3. MES系统集成和数据库操作
    /// 4. 测试配置管理和结果统计
    /// </summary>
    public partial class FormMain : UIForm
    {
        #region 全局变量属性

        /// <summary>
        /// 应用程序名称和版本信息，显示在窗体标题栏
        /// </summary>
        private string AppNameVersion { get; set; } = $"博实结-XL120Finish成品功能测试 - V:{Application.ProductVersion}";

        /// <summary>
        /// 线程安全的日志队列，用于异步处理日志输出
        /// 避免UI线程阻塞，提高界面响应性
        /// </summary>
        private readonly ConcurrentQueue<CustomLog> _logQueue = new ConcurrentQueue<CustomLog>();

        /// <summary>
        /// 日志处理定时器，定期从队列中取出日志并显示到界面
        /// </summary>
        private Timer _logTimer;

        /// <summary>
        /// 取消令牌源，用于控制测试流程的取消操作
        /// 当用户点击取消按钮时，通过此令牌通知所有异步任务停止执行
        /// </summary>
        private CancellationTokenSource Cts { get; set; }

        /// <summary>
        /// 测试配置对象，包含所有测试项目的配置信息
        /// 从TestConfig.json文件中加载，定义了测试流程和参数
        /// </summary>
        private TestConfig TestConfigBsj { get; set; }

        /// <summary>
        /// 用于界面显示的测试项目列表
        /// 将配置文件中的测试项转换为适合DataGridView显示的格式
        /// </summary>
        private List<ShowTestItem> ShowTestItemList { get; set; } = new List<ShowTestItem>();

        /// <summary>
        /// 当前正在测试的设备对象
        /// 包含设备SN码、测试结果、耗时等信息
        /// </summary>
        private BsjDevice TestDevice { get; set; }

        /// <summary>
        /// 临时存储的MAC地址
        /// 从扫码结果中提取，用于蓝牙连接等操作
        /// </summary>
        private string TmpMac { get; set; }

        /// <summary>
        /// 测试项目耗时统计字典
        /// 键为测试项名称，值为耗时（毫秒）
        /// </summary>
        private Dictionary<string, long> TestItemTimeDic { get; set; } = new Dictionary<string, long>();

        /// <summary>
        /// 设备通信串口对象
        /// 用于与被测设备进行AT指令通信
        /// </summary>
        private SerialPortStream SerialPortHelperDevice { get; set; }

        /// <summary>
        /// 电流串口对象
        /// 用于接收电流计读数
        /// </summary>
        private SerialPortStream SerialPortHelperCurrent { get; set; }

        /// <summary>
        /// 扫码枪串口对象
        /// 用于接收扫码枪扫描的SN码
        /// </summary>
        private SerialPortStream SerialPortHelperScan { get; set; }

        /// <summary>
        /// 设备串口数据临时存储缓冲区
        /// 用于累积接收到的串口数据，直到收到完整的响应
        /// </summary>
        private StringBuilder SbDevice { get; set; } = new StringBuilder();

        /// <summary>
        /// 测试总耗时（秒）
        /// 从测试开始到结束的总时间
        /// </summary>
        private int TestTime { get; set; }

        /// <summary>
        /// MES系统连接状态标志
        /// true表示已连接并验证通过，false表示离线模式
        /// </summary>
        private bool IsMesConnect { get; set; }

        /// <summary>
        /// 数据库连接状态标志
        /// true表示数据库连接正常，false表示无法连接数据库
        /// </summary>
        private bool IsDbConnect { get; set; }

        /// <summary>
        /// 重载Windows消息处理方法，用于监听USB设备插拔事件
        /// 当有串口设备插入或拔出时，自动刷新串口列表
        /// </summary>
        /// <param name="m">Windows消息结构体</param>
        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m); // 调用父类方法，确保其他消息正常处理

            switch (m.Msg)
            {
                case 0x219: // WM_DEVICECHANGE - 设备状态改变消息
                    switch ((int)m.WParam)
                    {
                        case 0x8000: // DBT_DEVICEARRIVAL - 检测到新设备插入
                            UpdateSerialPort(); // 刷新串口列表
                            break;

                        case 0x8004: // DBT_DEVICEREMOVECOMPLETE - 设备移除完成
                            UpdateSerialPort(); // 刷新串口列表
                            break;
                    }

                    break;
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 主窗体构造函数
        /// 初始化界面组件和基础配置
        /// </summary>
        public FormMain()
        {
            InitializeComponent(); // 初始化界面组件
            ShowDragStretch = true; // 启用窗体拖拽调整大小功能
            Setting.Current.Load(); // 加载应用程序配置
            InitializeLogTimer(); // 初始化日志处理定时器
        }

        #endregion

        #region UI控件事件

        /// <summary>
        /// 主窗体加载事件处理方法
        /// 在窗体显示时执行初始化操作
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void FormMain_Load(object sender, EventArgs e)
        {
            try
            {
                // 设置窗体标题为应用程序名称和版本
                Text = AppNameVersion;

                // 停止计时器（确保初始状态）
                timer_ShowTime.Stop();

                // 加载测试配置文件
                LoadTestConfig();

                // 从配置文件中恢复MES账号和站点名称
                uiTextBox_MesAccount.Text = Setting.Current.UserName;
                uiTextBox_MesStationName.Text = Setting.Current.StationName;

                // 初始化数据库连接
                DbInit();

                // 初始化MES系统连接
                MesInit();

                // 更新界面内容
                UpdateSerialPort(); // 刷新串口列表
                ShowTestItemsToListView(); // 显示测试项到右侧列表
                ShowTestItemToEditDgv(); // 显示测试项到编辑表格
            }
            catch (Exception ex)
            {
                // 记录异常信息到调试输出
                Debug.WriteLine(ex.ToString());
                await Task.Delay(0);
            }
        }

        /// <summary>
        /// 加载测试项配置文件
        /// 从JSON文件中读取测试配置并反序列化为对象
        /// </summary>
        private void LoadTestConfig()
        {
            // 构建配置文件路径
            string configPath = Application.StartupPath + "/config/TestConfig.json";

            // 读取JSON文件内容
            string json = File.ReadAllText(configPath);

            // 反序列化为测试配置对象
            TestConfigBsj = JsonConvert.DeserializeObject<TestConfig>(json);
        }

        /// <summary>
        /// 将启用的测试项目显示到右侧ListView控件中
        /// 只显示IsEnabled为true的测试项，用于测试过程中的状态显示
        /// </summary>
        private void ShowTestItemsToListView()
        {
            // 清空现有项目
            listView_TestItems.Items.Clear();
            int step = 0;

            // 遍历所有测试项组
            foreach (TestItem testItem in TestConfigBsj.Data)
            {
                // 遍历每个测试项组中的参数
                foreach (TestItemParameter param in testItem.Params)
                {
                    // 跳过禁用的测试参数
                    if (!param.IsEnabled)
                    {
                        continue;
                    }

                    // 为启用的测试项分配序号并添加到ListView
                    step++;
                    listView_TestItems.Items.Add($"{param.Name}", $"{step}.{param.Name}", 0);

                    // 调试输出：显示错误码和测试项对应关系
                    Debug.WriteLine($"ErrorCode: {param.ErrorCode},对应测试项=>: {param.Name}");
                }
            }
        }

        /// <summary>
        /// 将所有测试项目显示到编辑表格中
        /// 包括启用和禁用的项目，用于配置编辑
        /// </summary>
        private void ShowTestItemToEditDgv()
        {
            // 清空并重新构建测试项显示列表
            ShowTestItemList.Clear();
            int paramIndex = 0;

            // 遍历所有测试项组
            foreach (TestItem testItem in TestConfigBsj.Data)
            {
                // 遍历每个测试项组中的参数
                foreach (TestItemParameter param in testItem.Params)
                {
                    paramIndex++;

                    // 创建用于表格显示的测试项对象
                    ShowTestItem tmpData = new ShowTestItem
                    {
                        Index = paramIndex, // 序号
                        IsEnabled = param.IsEnabled, // 是否启用
                        SleepTime = testItem.SleepTime, // 延时时间
                        Name = param.Name, // 测试项名称
                        CmdStr = testItem.CmdStr, // 测试指令
                        AsciiSend = testItem.AsciiSend, // 是否ASCII发送
                        EnterLine = testItem.EnterLine, // 是否添加回车换行
                        ClearData = testItem.ClearData, // 是否清除旧数据
                        EndWithStr = testItem.EndWithStr, // 结束标识字符串
                        WaitTime = testItem.WaitTime, // 等待时间
                        WaitCount = testItem.WaitCount, // 等待次数
                        Retry = testItem.Retry, // 重试次数
                        Remark = param.Remark // 备注信息
                    };
                    ShowTestItemList.Add(tmpData);
                }
            }

            // 在UI线程中更新DataGridView显示
            Invoke(new Action(() =>
            {
                UIDataGridView dgv = uiDataGridView_TestItem;

                // 暂时隐藏表格以提高性能
                dgv.Visible = false;

                // 重新绑定数据源
                dgv.DataSource = null;
                dgv.DataSource = ShowTestItemList;

                // 设置列标题为中文
                if (dgv.Columns["Index"] != null) dgv.Columns["Index"].HeaderText = @"序号";
                if (dgv.Columns["IsEnabled"] != null) dgv.Columns["IsEnabled"].HeaderText = @"启用";
                if (dgv.Columns["Name"] != null) dgv.Columns["Name"].HeaderText = @"名称";
                if (dgv.Columns["CmdStr"] != null) dgv.Columns["CmdStr"].HeaderText = @"指令";
                if (dgv.Columns["AsciiSend"] != null) dgv.Columns["AsciiSend"].HeaderText = @"字符";
                if (dgv.Columns["EnterLine"] != null) dgv.Columns["EnterLine"].HeaderText = @"换行";
                if (dgv.Columns["ClearData"] != null) dgv.Columns["ClearData"].HeaderText = @"清除";
                if (dgv.Columns["EndWithStr"] != null) dgv.Columns["EndWithStr"].HeaderText = @"结束字符串";
                if (dgv.Columns["WaitTime"] != null) dgv.Columns["WaitTime"].HeaderText = @"等待时间";
                if (dgv.Columns["WaitCount"] != null) dgv.Columns["WaitCount"].HeaderText = @"等待次数";
                if (dgv.Columns["Retry"] != null) dgv.Columns["Retry"].HeaderText = @"重试次数";

                // 自动调整列宽以适应内容
                dgv.AutoResizeColumns();

                // 显示表格
                dgv.Visible = true;
            }));
        }

        /// <summary>
        /// 更新串口列表
        /// 扫描系统中可用的串口并更新到下拉框中
        /// 同时恢复之前保存的串口选择
        /// </summary>
        private void UpdateSerialPort()
        {
            // 清空现有的串口列表
            uiComboBox_SerialPortDevice.Items.Clear();
            uiComboBox_SerialPortScan.Items.Clear();

            // 获取系统中所有可用串口并按名称排序
            string[] ports = SerialPort.GetPortNames().OrderBy(x => x).ToArray();

            // 将串口添加到两个下拉框中
            foreach (string port in ports)
            {
                uiComboBox_SerialPortDevice.Items.Add(port); // 设备通信串口
                uiComboBox_SerialPortScan.Items.Add(port); // 扫码枪串口
            }

            // 恢复之前保存的串口选择，如果保存的串口不存在则选择第一个可用串口
            uiComboBox_SerialPortDevice.Text = uiComboBox_SerialPortDevice.Items.Contains(Setting.Current.SerialPortDevice)
                ? Setting.Current.SerialPortDevice
                : ports.FirstOrDefault();

            uiComboBox_SerialPortScan.Text = uiComboBox_SerialPortScan.Items.Contains(Setting.Current.SerialPortScan)
                ? Setting.Current.SerialPortScan
                : ports.FirstOrDefault();
        }

        /// <summary>
        /// 初始化数据库连接
        /// 测试数据库连接状态并更新界面显示
        /// </summary>
        private void DbInit()
        {
            try
            {
                // 尝试查询数据库以测试连接
                TestDetail detail = DbHelper.FSql.Select<TestDetail>().First();
                IsDbConnect = detail != null;
            }
            catch
            {
                // 数据库连接失败
                IsDbConnect = false;
            }

            // 更新数据库连接状态开关
            uiSwitch_DBS.Active = IsDbConnect;

            if (IsDbConnect)
            {
                ShowLog(@"数据库连接已打开.", LogType.Info);
            }
            else
            {
                string tips = "数据库连接失败.若继续测试，将不记录测试结果到数据库.";
                ShowLog(tips, LogType.Error);

                // 异步显示错误对话框，避免阻塞UI线程
                Task.Run(() => this.ShowErrorDialog("错误提醒", tips));
            }
        }

        /// <summary>
        /// 初始化MES系统连接
        /// 验证MES账号并设置相关测试项的启用状态
        /// </summary>
        private void MesInit()
        {
            // 设置MES用户名
            MESService.UserName = Setting.Current.UserName;

            // 验证MES账号
            CallBackResultModel<ReturnDataModel> result = MESService.ValidateAccount();
            IsMesConnect = result.Success;

            // 更新MES连接状态开关
            uiSwitch_MES.Active = IsMesConnect;

            // 根据MES连接状态设置MES过站测试项的启用状态
            TestConfigBsj.Data.Find(x => x.Name == "MES过站").Params.Find(x => x.Name == "MES过站").IsEnabled = IsMesConnect;

            // 显示MES账号验证结果
            ShowLog($@"MES账号验证:{MESService.UserName} ,{result.Msg}");

            if (IsMesConnect)
            {
                ShowLog(@"MES连接已打开.", LogType.Info);
            }
            else
            {
                // MES连接失败，显示错误对话框并切换到离线模式
                this.ShowErrorDialog("MES账号验证失败", "请联系管理员，即将切换为离线测试模式");
            }
        }

        /// <summary>
        /// 串口开关按钮点击事件处理方法
        /// 根据当前状态打开或关闭串口连接
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void uiButton_SerialPortOpenClose_Click(object sender, EventArgs e)
        {
            if (uiButton_SerialPortOpenClose.Text == @"打开串口")
            {
                // 尝试打开串口
                if (!SerialPortOpen())
                {
                    // 打开失败，关闭已打开的串口
                    SerialPortClose();
                    return;
                }

                // 打开成功，更新按钮状态
                uiButton_SerialPortOpenClose.Text = @"关闭串口";
                uiButton_SerialPortOpenClose.Style = UIStyle.Green;
            }
            else
            {
                // 关闭串口
                if (SerialPortClose())
                {
                    // 关闭成功，更新按钮状态
                    uiButton_SerialPortOpenClose.Text = @"打开串口";
                    uiButton_SerialPortOpenClose.Style = UIStyle.Blue;
                }
            }
        }

        /// <summary>
        /// 打开串口连接
        /// 同时打开设备通信串口和扫码枪串口
        /// </summary>
        /// <returns>true表示成功，false表示失败</returns>
        private bool SerialPortOpen()
        {
            // 保存当前选择的串口配置
            Setting.Current.SerialPortDevice = uiComboBox_SerialPortDevice.Text;
            Setting.Current.SerialPortScan = uiComboBox_SerialPortScan.Text;
            Setting.Current.Save();

            // 创建并配置设备通信串口
            try
            {
                SerialPortHelperDevice = new SerialPortStream(Setting.Current.SerialPortDevice);
                SerialPortHelperDevice.BaudRate = Setting.Current.SerialPortDeviceBaudRate; // 波特率
                SerialPortHelperDevice.DataBits = 8; // 数据位
                SerialPortHelperDevice.StopBits = StopBits.One; // 停止位
                SerialPortHelperDevice.Parity = Parity.None; // 校验位
                SerialPortHelperDevice.DataReceived += SerialPortDataReceivedDevice; // 数据接收事件
                SerialPortHelperDevice.ErrorReceived += SerialPortErrorDevice; // 错误事件
                SerialPortHelperDevice.DtrEnable = true; // 启用DTR信号
                SerialPortHelperDevice.RtsEnable = true; // 启用RTS信号
                SerialPortHelperDevice.ReadTimeout = 1000; // 读取超时
                SerialPortHelperDevice.WriteTimeout = 1000; // 写入超时
                SerialPortHelperDevice.Open(); // 打开串口

                ShowLog($"串口{Ai.中括号左}{SerialPortHelperDevice.PortName}{Ai.中括号右}打开成功.", LogType.Info);
            }
            catch (Exception ex)
            {
                ShowLog($"串口{Ai.中括号左}{SerialPortHelperDevice?.PortName}{Ai.中括号右}打开失败.{ex}", LogType.Error);
                return false;
            }

            // 创建并配置电流通信串口
            try
            {
                SerialPortHelperCurrent = new SerialPortStream(Setting.Current.SerialPortCurrent);
                SerialPortHelperCurrent.BaudRate = Setting.Current.SerialPortCurrentBaudRate; // 波特率
                SerialPortHelperCurrent.DataBits = 8; // 数据位
                SerialPortHelperCurrent.StopBits = StopBits.One; // 停止位
                SerialPortHelperCurrent.Parity = Parity.None; // 校验位
                SerialPortHelperCurrent.DataReceived += SerialPortDataReceivedCurrent; // 数据接收事件
                SerialPortHelperCurrent.ErrorReceived += SerialPortErrorCurrent; // 错误事件
                SerialPortHelperCurrent.DtrEnable = true; // 启用DTR信号
                SerialPortHelperCurrent.RtsEnable = true; // 启用RTS信号
                SerialPortHelperCurrent.ReadTimeout = 1000; // 读取超时
                SerialPortHelperCurrent.WriteTimeout = 1000; // 写入超时
                SerialPortHelperCurrent.Open(); // 打开串口

                ShowLog($"串口{Ai.中括号左}{SerialPortHelperCurrent.PortName}{Ai.中括号右}打开成功.", LogType.Info);
            }
            catch (Exception ex)
            {
                ShowLog($"串口{Ai.中括号左}{SerialPortHelperCurrent?.PortName}{Ai.中括号右}打开失败.{ex}", LogType.Error);
                return false;
            }

            // 创建并配置扫码枪串口
            try
            {
                SerialPortHelperScan = new SerialPortStream(Setting.Current.SerialPortScan);
                SerialPortHelperScan.BaudRate = Setting.Current.SerialPortScanBaudRate; // 波特率
                SerialPortHelperScan.DataBits = 8; // 数据位
                SerialPortHelperScan.StopBits = StopBits.One; // 停止位
                SerialPortHelperScan.Parity = Parity.None; // 校验位
                SerialPortHelperScan.DataReceived += SerialPortDataReceivedScan; // 数据接收事件
                SerialPortHelperScan.ErrorReceived += SerialPortErrorScan; // 错误事件
                SerialPortHelperScan.DtrEnable = true; // 启用DTR信号
                SerialPortHelperScan.RtsEnable = true; // 启用RTS信号
                SerialPortHelperScan.ReadTimeout = 1000; // 读取超时
                SerialPortHelperScan.WriteTimeout = 1000; // 写入超时
                SerialPortHelperScan.Open(); // 打开串口

                ShowLog($"串口{Ai.中括号左}{SerialPortHelperScan.PortName}{Ai.中括号右}打开成功.", LogType.Info);
            }
            catch (Exception ex)
            {
                ShowLog($"串口{Ai.中括号左}{SerialPortHelperScan?.PortName}{Ai.中括号右}打开失败.{ex}", LogType.Error);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 关闭串口连接
        /// 同时关闭设备通信串口和扫码枪串口
        /// </summary>
        /// <returns>true表示成功，false表示失败</returns>
        private bool SerialPortClose()
        {
            bool success = true;

            // 关闭设备通信串口
            if (SerialPortHelperDevice != null && SerialPortHelperDevice.IsOpen)
            {
                try
                {
                    SerialPortHelperDevice.Close();
                    ShowLog($"串口{Ai.中括号左}{SerialPortHelperDevice.PortName}{Ai.中括号右}关闭成功.", LogType.Info);
                }
                catch (Exception ex)
                {
                    ShowLog($"串口{Ai.中括号左}{SerialPortHelperDevice.PortName}{Ai.中括号右}关闭失败.{ex}");
                    success = false;
                }
            }

            // 关闭电流通信串口
            if (SerialPortHelperCurrent != null && SerialPortHelperCurrent.IsOpen)
            {
                try
                {
                    SerialPortHelperCurrent.Close();
                    ShowLog($"串口{Ai.中括号左}{SerialPortHelperCurrent.PortName}{Ai.中括号右}关闭成功.", LogType.Info);
                }
                catch (Exception ex)
                {
                    ShowLog($"串口{Ai.中括号左}{SerialPortHelperCurrent.PortName}{Ai.中括号右}关闭失败.{ex}");
                    success = false;
                }
            }

            // 关闭扫码枪串口
            if (SerialPortHelperScan != null && SerialPortHelperScan.IsOpen)
            {
                try
                {
                    SerialPortHelperScan.Close();
                    ShowLog($"串口{Ai.中括号左}{SerialPortHelperScan.PortName}{Ai.中括号右}关闭成功.", LogType.Info);
                }
                catch (Exception ex)
                {
                    ShowLog($"串口{Ai.中括号左}{SerialPortHelperScan.PortName}{Ai.中括号右}关闭失败.{ex}");
                    success = false;
                }
            }

            return success;
        }

        /// <summary>
        /// MES连接开关点击事件处理方法
        /// 切换MES系统的连接状态
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void uiSwitch_MES_Click(object sender, EventArgs e)
        {
            if (IsMesConnect)
            {
                // 当前已连接，执行断开操作
                IsMesConnect = false;
                ShowLog("MES连接已关闭.", LogType.Error);
                uiSwitch_MES.Active = IsMesConnect;

                // 禁用MES过站测试项
                TestConfigBsj.Data.Find(x => x.Name == "MES过站").Params.Find(x => x.Name == "MES过站").IsEnabled = IsMesConnect;
            }
            else
            {
                // 当前未连接，尝试重新连接
                // 保存用户输入的账号和站点信息
                Setting.Current.UserName = uiTextBox_MesAccount.Text;
                Setting.Current.StationName = uiTextBox_MesStationName.Text;
                Setting.Current.Save();

                // 重新初始化MES连接
                MesInit();
            }

            // 无论连接还是断开，都需要刷新界面显示
            ShowTestItemsToListView(); // 更新测试项列表
            ShowTestItemToEditDgv(); // 更新编辑表格
        }

        /// <summary>
        /// 数据库连接开关点击事件处理方法
        /// 切换数据库的连接状态
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void uiSwitch_DBS_Click(object sender, EventArgs e)
        {
            if (IsDbConnect)
            {
                // 当前已连接，执行断开操作
                IsDbConnect = false;
                ShowLog("数据库连接已关闭.", LogType.Error);
                uiSwitch_DBS.Active = IsDbConnect;
            }
            else
            {
                // 当前未连接，尝试重新连接
                DbInit();
            }
        }

        /// <summary>
        /// 测试时间显示定时器事件处理方法
        /// 每秒更新一次测试耗时显示
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void timer_ShowTime_Tick(object sender, EventArgs e)
        {
            try
            {
                TestTime++; // 测试时间递增（秒）
                uiLabel_Status.Text = $@"{TestTime}"; // 更新状态标签显示
            }
            catch (Exception ex)
            {
                ShowLog(ex.ToString(), LogType.Error);
            }
        }

        /// <summary>
        /// 测试项编辑表格双击事件处理方法
        /// 双击表格行时打开测试项编辑对话框
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数，包含被点击的行列信息</param>
        private void uiDataGridView_TestItem_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            // 检查是否点击了有效的行（排除表头）
            if (e.RowIndex < 0)
            {
                return;
            }

            // 使用LINQ语法根据行索引查找对应的测试项和参数
            // 这里需要将嵌套的测试项结构展平，然后根据总索引找到对应项
            var foundItem = TestConfigBsj.Data
                .SelectMany((testItem, tmpTestItemIndex) =>
                    testItem.Params.Select((param, tmpParamIndex) =>
                        new { testItem, tmpTestItemIndex, param, tmpParamIndex }))
                .Where((item, totalIndex) => totalIndex == e.RowIndex)
                .FirstOrDefault();

            // 确保找到了对应的项目
            Debug.Assert(foundItem != null, nameof(foundItem) + " != null");
            int testItemIndex = foundItem.tmpTestItemIndex; // 测试项组索引
            int paramIndex = foundItem.tmpParamIndex; // 参数索引

            // 创建编辑对话框选项，配置测试项的各种参数
            BsjHelperV2.UI.UIEditOption option = new BsjHelperV2.UI.UIEditOption
            {
                AutoLabelWidth = true, // 自动调整标签宽度
                Text = "编辑测试项" // 对话框标题
            };

            // 添加测试项组级别的配置选项
            option.AddText("TestItemName", "一级名称", TestConfigBsj.Data[testItemIndex].Name, false);
            option.AddText("TestItemCmdStr", "指令内容", TestConfigBsj.Data[testItemIndex].CmdStr, false);
            option.AddSwitch("TestItemSwitchAsciiSend", "ASCII发送", TestConfigBsj.Data[testItemIndex].AsciiSend, "打开", "关闭");
            option.AddSwitch("TestItemSwitchEnterLine", "加回车换行", TestConfigBsj.Data[testItemIndex].EnterLine, "打开", "关闭");
            option.AddSwitch("TestItemSwitchClearData", "先清除旧数据", TestConfigBsj.Data[testItemIndex].ClearData, "打开", "关闭");
            option.AddText("TestItemEndWithStr", "结束字符串", TestConfigBsj.Data[testItemIndex].EndWithStr, false);
            option.AddInteger("TestItemWaitTime", "等待时间", TestConfigBsj.Data[testItemIndex].WaitTime);
            option.AddInteger("TestItemWaitCount", "等待次数", TestConfigBsj.Data[testItemIndex].WaitCount);
            option.AddInteger("TestItemRetry", "失败重试次数", TestConfigBsj.Data[testItemIndex].Retry);
            option.AddInteger("SleepTime", "延时执行(毫秒)", TestConfigBsj.Data[testItemIndex].SleepTime);

            // 添加测试参数级别的配置选项
            option.AddText("ParamName", "二级名称", TestConfigBsj.Data[testItemIndex].Params[paramIndex].Name, false);
            option.AddSwitch("ParamSwitch", "检测状态", TestConfigBsj.Data[testItemIndex].Params[paramIndex].IsEnabled, "打开", "关闭");
            option.AddText("ParamRegexPattern", "正则表达式", TestConfigBsj.Data[testItemIndex].Params[paramIndex].RegexPattern, false);

            // 数据截取类型下拉框
            string[] dataSourceEnumCutTextType = Enum.GetValues(typeof(EnumCutTextType))
                .Cast<EnumCutTextType>()
                .Select(x => x.ToString())
                .ToArray();
            option.AddCombobox("ParamCutTextType", "数据截取", dataSourceEnumCutTextType,
                (int)TestConfigBsj.Data[testItemIndex].Params[paramIndex].CutTextType);

            option.AddText("ParamCutTextFlag1", "截取标识1", TestConfigBsj.Data[testItemIndex].Params[paramIndex].CutTextFlag1, false);
            option.AddText("ParamCutTextFlag2", "截取标识2", TestConfigBsj.Data[testItemIndex].Params[paramIndex].CutTextFlag2, false);
            option.AddSwitch("ParamSwitchIsCompareText", "比较文本", TestConfigBsj.Data[testItemIndex].Params[paramIndex].IsCompareText, "打开", "关闭");
            option.AddText("ParamTargetValue", "比较目标值", TestConfigBsj.Data[testItemIndex].Params[paramIndex].TargetValue, false);

            // 文本比较类型下拉框
            string[] dataSourceEnumCompareTextType = Enum.GetValues(typeof(EnumCompareTextType))
                .Cast<EnumCompareTextType>()
                .Select(x => x.ToString())
                .ToArray();
            option.AddCombobox("ParamCompareTextType", "文本比较", dataSourceEnumCompareTextType,
                (int)TestConfigBsj.Data[testItemIndex].Params[paramIndex].CompareTextType);

            option.AddDouble("ParamRestoreBase", "还原基数", TestConfigBsj.Data[testItemIndex].Params[paramIndex].RestoreBase);
            option.AddDouble("ParamMinValue", "最小值", TestConfigBsj.Data[testItemIndex].Params[paramIndex].MinValue);
            option.AddDouble("ParamMaxValue", "最大值", TestConfigBsj.Data[testItemIndex].Params[paramIndex].MaxValue);
            option.AddText("ParamUnit", "单位", TestConfigBsj.Data[testItemIndex].Params[paramIndex].Unit, false);
            option.AddText("ParamErrorCode", "错误码", TestConfigBsj.Data[testItemIndex].Params[paramIndex].ErrorCode, false);
            option.AddText("ParamRemark", "备注", TestConfigBsj.Data[testItemIndex].Params[paramIndex].Remark, false);

            // 创建并显示编辑对话框
            BsjUiEditForm frm = new BsjUiEditForm(option);
            frm.Render(); // 渲染界面
            frm.ShowDialog(); // 模态显示对话框

            // 检查用户是否点击了确定按钮
            if (frm.DialogResult != DialogResult.OK)
            {
                return; // 用户取消了编辑
            }

            // 用户确认编辑后，将对话框中的值更新到测试配置对象

            // 更新测试项组级别的配置
            TestConfigBsj.Data[testItemIndex].Name = frm["TestItemName"].ToString();
            TestConfigBsj.Data[testItemIndex].CmdStr = frm["TestItemCmdStr"].ToString();
            TestConfigBsj.Data[testItemIndex].AsciiSend = Convert.ToBoolean(frm["TestItemSwitchAsciiSend"]);
            TestConfigBsj.Data[testItemIndex].EnterLine = Convert.ToBoolean(frm["TestItemSwitchEnterLine"]);
            TestConfigBsj.Data[testItemIndex].ClearData = Convert.ToBoolean(frm["TestItemSwitchClearData"]);
            TestConfigBsj.Data[testItemIndex].EndWithStr = frm["TestItemEndWithStr"].ToString();
            TestConfigBsj.Data[testItemIndex].WaitTime = Convert.ToInt32(frm["TestItemWaitTime"]);
            TestConfigBsj.Data[testItemIndex].WaitCount = Convert.ToInt32(frm["TestItemWaitCount"]);
            TestConfigBsj.Data[testItemIndex].Retry = Convert.ToInt32(frm["TestItemRetry"]);
            TestConfigBsj.Data[testItemIndex].SleepTime = Convert.ToInt32(frm["SleepTime"]);

            // 更新测试参数级别的配置
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].Name = frm["ParamName"].ToString();
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].IsEnabled = Convert.ToBoolean(frm["ParamSwitch"]);
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].RegexPattern = frm["ParamRegexPattern"].ToString();
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].CutTextType =
                (EnumCutTextType)Enum.Parse(typeof(EnumCutTextType), frm["ParamCutTextType"].ToString());
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].CutTextFlag1 = frm["ParamCutTextFlag1"].ToString();
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].CutTextFlag2 = frm["ParamCutTextFlag2"].ToString();
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].IsCompareText = Convert.ToBoolean(frm["ParamSwitchIsCompareText"]);
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].TargetValue = frm["ParamTargetValue"].ToString();
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].CompareTextType =
                (EnumCompareTextType)Enum.Parse(typeof(EnumCompareTextType), frm["ParamCompareTextType"].ToString());
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].RestoreBase = Convert.ToDouble(frm["ParamRestoreBase"]);
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].MinValue = Convert.ToDouble(frm["ParamMinValue"]);
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].MaxValue = Convert.ToDouble(frm["ParamMaxValue"]);
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].Unit = frm["ParamUnit"].ToString();
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].ErrorCode = frm["ParamErrorCode"].ToString();
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].Remark = frm["ParamRemark"].ToString();

            // 将修改后的配置保存到JSON文件
            // 注释掉的代码是一些特殊的动态配置示例，用于某些特定测试项的动态参数替换
            string json = JsonConvert.SerializeObject(TestConfigBsj, Formatting.Indented);
            string configPath = Application.StartupPath + "/config/TestConfig.json";
            File.WriteAllText(configPath, json);

            // 重新加载配置并更新界面显示
            LoadTestConfig(); // 重新加载配置文件
            ShowTestItemsToListView(); // 更新测试项列表
            ShowTestItemToEditDgv(); // 更新编辑表格

            // 检查并更新MES开关状态
            // 如果MES过站测试项被启用/禁用，需要同步更新MES连接状态
            foreach (TestItem item in TestConfigBsj.Data)
            {
                foreach (TestItemParameter parameter in item.Params)
                {
                    if (parameter.Name.Equals("MES过站"))
                    {
                        IsMesConnect = parameter.IsEnabled;
                        uiSwitch_MES.Active = IsMesConnect;
                    }
                }
            }
        }

        /// <summary>
        /// 取消测试按钮点击事件处理方法
        /// 通过取消令牌停止正在进行的测试流程
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void uiButton_Cancel_Click(object sender, EventArgs e)
        {
            if (Cts != null)
            {
                Cts.Cancel(); // 发送取消信号，停止测试流程
            }
        }

        #endregion

        #region 设备串口事件

        /// <summary>
        /// 设备通信串口数据接收事件处理方法
        /// 接收设备返回的测试数据，使用字节数组方式读取以确保数据完整性
        /// </summary>
        /// <param name="sender">事件发送者（串口对象）</param>
        /// <param name="e">串口数据接收事件参数</param>
        private void SerialPortDataReceivedDevice(object sender, SerialDataReceivedEventArgs e)
        {
            SerialPortStream spb = (SerialPortStream)sender;
            try
            {
                // 创建字节数组接收缓冲区，大小为待读取的字节数
                byte[] receivedData = new byte[spb.BytesToRead];

                // 从串口读取数据到字节数组
                int count = spb.Read(receivedData, 0, receivedData.Length);
                Debug.WriteLine($@"Received Count: {count}");

                // 将字节数组转换为字符串（使用系统默认编码）
                string recData = Encoding.Default.GetString(receivedData);
                // 备用方案：使用ReadExisting()方法（已注释）
                // string recData = spb.ReadExisting();

                // 可选：移除不可见控制字符（已注释）
                // recData = new string(recData.Where(c => !Char.IsControl(c)).ToArray());

                // 检查接收到的数据是否为空
                if (string.IsNullOrWhiteSpace(recData))
                {
                    return;
                }

                // 将接收到的数据追加到设备数据缓冲区
                SbDevice.Append(recData);

                // 显示接收到的数据（不换行）
                ShowLog(recData, enterLine: false);
            }
            catch (Exception ex)
            {
                ShowLog(spb.PortName + @":接收数据异常，" + ex.Message, LogType.Error);
            }
        }

        /// <summary>
        /// 设备通信串口错误事件处理方法
        /// 当设备串口发生错误时记录错误信息
        /// </summary>
        /// <param name="sender">事件发送者（串口对象）</param>
        /// <param name="e">串口错误事件参数</param>
        private void SerialPortErrorDevice(object sender, SerialErrorReceivedEventArgs e)
        {
            try
            {
                ShowLog(SerialPortHelperDevice.PortName + @":串口错误，" + e.EventType, LogType.Error);
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex.ToString());
            }
        }

        /// <summary>
        /// 向设备串口发送数据
        /// 支持ASCII字符串和HEX字符串两种发送模式
        /// </summary>
        /// <param name="comStr">要发送的命令字符串</param>
        /// <param name="asciiSend">是否以ASCII模式发送（true=ASCII，false=HEX）</param>
        /// <param name="enterLine">是否在命令末尾添加回车换行符</param>
        private void SerialPortWriteDevice(string comStr, bool asciiSend = true, bool enterLine = true)
        {
            try
            {
                // 记录发送的指令（去除回车换行符以便显示）
                ShowLog($"向{Ai.中括号左}{SerialPortHelperDevice.PortName}{Ai.中括号右}发送指令:{Ai.中括号左}{comStr.Replace("\r\n", "")}{Ai.中括号右}");

                if (asciiSend)
                {
                    // ASCII模式发送
                    if (enterLine)
                    {
                        comStr += "\r\n"; // 添加回车换行符
                    }

                    // 将字符串转换为UTF-8字节数组
                    byte[] dataToSend = Encoding.UTF8.GetBytes(comStr);

                    // 写入数据到串口
                    SerialPortHelperDevice.Write(dataToSend, 0, dataToSend.Length);
                }
                else
                {
                    // HEX模式发送
                    comStr = comStr.Replace(" ", ""); // 移除空格
                    if (enterLine)
                    {
                        comStr += "\r\n"; // 添加回车换行符
                    }

                    // 将HEX字符串转换为字节数组
                    byte[] dataToSend = BsjHelper.HexToByte(comStr);

                    // 写入数据到串口
                    SerialPortHelperDevice.Write(dataToSend, 0, dataToSend.Length);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }
        }

        #endregion

        #region 电流串口事件

        /// <summary>
        /// 电流通信串口数据接收事件处理方法
        /// 接收设备返回的测试数据，使用字节数组方式读取以确保数据完整性
        /// </summary>
        /// <param name="sender">事件发送者（串口对象）</param>
        /// <param name="e">串口数据接收事件参数</param>
        private void SerialPortDataReceivedCurrent(object sender, SerialDataReceivedEventArgs e)
        {
            SerialPortStream spb = (SerialPortStream)sender;
            try
            {
                // 创建字节数组接收缓冲区，大小为待读取的字节数
                byte[] receivedData = new byte[spb.BytesToRead];

                // 从串口读取数据到字节数组
                int count = spb.Read(receivedData, 0, receivedData.Length);
                Debug.WriteLine($@"Received Count: {count}");

                // 将字节数组转换为字符串（使用系统默认编码）
                string recData = Encoding.Default.GetString(receivedData);
                // 备用方案：使用ReadExisting()方法（已注释）
                // string recData = spb.ReadExisting();

                // 可选：移除不可见控制字符（已注释）
                // recData = new string(recData.Where(c => !Char.IsControl(c)).ToArray());

                // 检查接收到的数据是否为空
                if (string.IsNullOrWhiteSpace(recData))
                {
                    return;
                }

                // 将接收到的数据追加到设备数据缓冲区
                SbDevice.Append(recData);

                // 显示接收到的数据（不换行）
                ShowLog(recData, enterLine: false);
            }
            catch (Exception ex)
            {
                ShowLog(spb.PortName + @":接收数据异常，" + ex.Message, LogType.Error);
            }
        }

        /// <summary>
        /// 设备通信串口错误事件处理方法
        /// 当设备串口发生错误时记录错误信息
        /// </summary>
        /// <param name="sender">事件发送者（串口对象）</param>
        /// <param name="e">串口错误事件参数</param>
        private void SerialPortErrorCurrent(object sender, SerialErrorReceivedEventArgs e)
        {
            try
            {
                ShowLog(SerialPortHelperCurrent.PortName + @":串口错误，" + e.EventType, LogType.Error);
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex.ToString());
            }
        }

        /// <summary>
        /// 向设备串口发送数据
        /// 支持ASCII字符串和HEX字符串两种发送模式
        /// </summary>
        /// <param name="comStr">要发送的命令字符串</param>
        /// <param name="asciiSend">是否以ASCII模式发送（true=ASCII，false=HEX）</param>
        /// <param name="enterLine">是否在命令末尾添加回车换行符</param>
        private void SerialPortWriteCurrent(string comStr, bool asciiSend = true, bool enterLine = true)
        {
            try
            {
                // 记录发送的指令（去除回车换行符以便显示）
                ShowLog($"向{Ai.中括号左}{SerialPortHelperCurrent.PortName}{Ai.中括号右}发送指令:{Ai.中括号左}{comStr.Replace("\r\n", "")}{Ai.中括号右}");

                if (asciiSend)
                {
                    // ASCII模式发送
                    if (enterLine)
                    {
                        comStr += "\r\n"; // 添加回车换行符
                    }

                    // 将字符串转换为UTF-8字节数组
                    byte[] dataToSend = Encoding.UTF8.GetBytes(comStr);

                    // 写入数据到串口
                    SerialPortHelperCurrent.Write(dataToSend, 0, dataToSend.Length);
                }
                else
                {
                    // HEX模式发送
                    comStr = comStr.Replace(" ", ""); // 移除空格
                    if (enterLine)
                    {
                        comStr += "\r\n"; // 添加回车换行符
                    }

                    // 将HEX字符串转换为字节数组
                    byte[] dataToSend = BsjHelper.HexToByte(comStr);

                    // 写入数据到串口
                    SerialPortHelperCurrent.Write(dataToSend, 0, dataToSend.Length);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }
        }

        #endregion

        #region 扫码枪串口事件

        /// <summary>
        /// 扫码枪串口数据接收事件处理方法
        /// 接收扫码枪扫描的条码数据，解析SN和MAC地址，并启动测试流程
        /// </summary>
        /// <param name="sender">事件发送者（串口对象）</param>
        /// <param name="e">串口数据接收事件参数</param>
        private void SerialPortDataReceivedScan(object sender, SerialDataReceivedEventArgs e)
        {
            SerialPortStream spb = (SerialPortStream)sender;
            try
            {
                // 读取扫码枪数据
                byte[] receivedData = new byte[spb.BytesToRead];
                int count = spb.Read(receivedData, 0, receivedData.Length);
                string recData = Encoding.Default.GetString(receivedData).Trim(); // 去除首尾空白字符
                Debug.WriteLine($@"Received Count: {count}");

                // 显示扫码结果和时间戳
                ShowLog($@"{DateTime.Now:yyyy-MM-dd HH:mm:ss:fff}{Environment.NewLine}扫码结果:{recData}");

                // 检查是否有设备正在测试中（防止重复测试）
                if (TestDevice != null)
                {
                    ShowLog($@"请等待上一个设备{Ai.中括号左}{TestDevice.Sn}{Ai.中括号右}检测完毕.");
                    return;
                }

                // 验证扫码数据长度是否符合配置要求
                if (recData.Length != Setting.Current.SnLength)
                {
                    ShowLog($@"Sn:{Ai.中括号左}{recData}{Ai.中括号右}长度不正确.请核对检查.", LogType.Error);
                    return;
                }

                // 从扫码数据中分离SN和MAC地址
                // 假设格式为：SN + MAC（MAC占最后12位）
                TmpMac = recData.Substring(recData.Length - 12, 12); // 提取MAC地址
                string sn = recData.Substring(0, recData.Length - 12); // 提取SN序列号

                // 如果MES系统已连接，进行MES过站验证
                if (IsMesConnect)
                {
                    // 调用MES系统验证条码和工站信息
                    CallBackResultModel<string> result = MESService.ValidateBarCode(sn, Setting.Current.StationName);
                    if (!result.Success)
                    {
                        // MES验证失败，记录错误并显示对话框
                        ShowLog("MES查站失败:" + result.Msg, LogType.Error);
                        this.ShowErrorDialog("MES查站失败", result.Msg);
                        return;
                    }

                    // MES验证成功，记录成功信息
                    ShowLog("MES查站成功:" + Environment.NewLine + result.Msg, LogType.Error);
                }

                // 创建测试设备对象，包含SN和项目信息
                TestDevice = new BsjDevice { Sn = sn, Project = TestConfigBsj.Project };

                // 在后台线程启动测试工作流程
                Task.Run(DoWork);
            }
            catch (Exception ex)
            {
                ShowLog(spb.PortName + @":接收数据异常，" + ex.Message, LogType.Error);
            }
        }

        /// <summary>
        /// 扫码枪串口错误事件处理方法
        /// 当扫码枪串口发生错误时记录错误信息
        /// </summary>
        /// <param name="sender">事件发送者（串口对象）</param>
        /// <param name="e">串口错误事件参数</param>
        private void SerialPortErrorScan(object sender, SerialErrorReceivedEventArgs e)
        {
            try
            {
                ShowLog(SerialPortHelperDevice.PortName + @":串口错误，" + e.EventType, LogType.Error);
            }
            catch (Exception ex)
            {
                ShowLog(ex.ToString());
            }
        }

        // /// <summary>
        // /// 串口接收数据处理
        // /// </summary>
        // /// <param name="sender">串口助手类对象</param>
        // /// <param name="arrData">接收数据数组</param>
        // private void SerialPortDataReceivedProcessScan(object sender, byte[] arrData)
        // {
        //     SerialPortHelper spb = (SerialPortHelper)sender;
        //     Invoke(new Action(() =>
        //     {
        //         try
        //         {
        //             string strData = SerialData.ToString(arrData);
        //             // string hexData = SerialData.ToHexString(arrData);
        //
        //             string recData = strData.Trim();
        //
        //             // 显示扫码结果
        //             ShowLog($@"扫码结果:{recData}");
        //
        //             // 判断是否正在检测其他设备
        //             if (TestDevice != null)
        //             {
        //                 ShowLog($"请等待上一个设备{Ai.中括号左}{TestDevice.Sn}{Ai.中括号右}检测完毕", LogType.Error);
        //                 return;
        //             }
        //
        //             // 校验接收到的SN长度
        //             if (!recData.Length.Equals(Setting.Current.SnLength))
        //             {
        //                 ShowLog($@"Sn:{Ai.中括号左}{recData}{Ai.中括号右}长度不正确.请核对检查.", LogType.Error);
        //                 return;
        //             }
        //
        //             // 校验MES工序
        //             if (IsMesConnect)
        //             {
        //                 CallBackResultModel<string> result = MESService.ValidateBarCode(recData, Setting.Current.StationName);
        //                 if (!result.Success)
        //                 {
        //                     ShowLog($"MES查站失败:{result.Msg}", LogType.Error);
        //                     this.ShowErrorDialog("MES查站失败", result.Msg);
        //                     return;
        //                 }
        //
        //                 ShowLog($"MES查站成功:{Environment.NewLine}{result.Msg}", LogType.Error);
        //             }
        //
        //             // 创建检测对象
        //             TestDevice = new TestDevice { Sn = recData };
        //
        //             // 启动测试线程
        //             Task.Run(DoWork);
        //         }
        //         catch (Exception ex)
        //         {
        //             Console.WriteLine(spb.ConfigSerialPort.PortName + @":接收数据异常，" + ex.Message);
        //         }
        //     }));
        // }
        //
        // /// <summary>
        // /// 串口错误事件
        // /// </summary>
        // /// <param name="sender">串口助手类对象</param>
        // /// <param name="enumError">错误枚举</param>
        // /// <param name="strError">错误内容</param>
        // private void SerialPortErrorProcessScan(object sender, enumSerialError enumError, string strError)
        // {
        //     Invoke(new Action(() =>
        //     {
        //         SerialPortHelper spb = (SerialPortHelper)sender;
        //         switch (enumError)
        //         {
        //             case enumSerialError.LinkError:
        //                 spb.CloseCom(out string str);
        //                 Console.WriteLine(spb.SerialMark + @"串口错误：" + strError);
        //                 Console.WriteLine(str);
        //                 //MessageBox.Show(strError, "串口错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        //                 break;
        //             case enumSerialError.WriteError:
        //                 Console.WriteLine(spb.SerialMark + @"发送错误：" + strError);
        //                 break;
        //             case enumSerialError.ReceivedError:
        //                 Console.WriteLine(spb.SerialMark + @"接收错误：" + strError);
        //                 break;
        //         }
        //     }));
        // }

        #endregion

        #region 检测逻辑

        /// <summary>
        /// 测试主工作流程方法
        /// 这是整个测试系统的核心方法，负责执行完整的测试流程
        /// 包括初始化、遍历测试项、发送指令、接收数据、数据解析和结果判定
        /// </summary>
        private async void DoWork()
        {
            try
            {
                // 初始化测试环境和参数
                Cts = new CancellationTokenSource(); // 创建取消令牌源，用于中断测试
                TestTime = 0; // 重置测试时间计数器
                SbDevice.Clear(); // 清空设备数据缓冲区
                Invoke(new Action(() => { uiRichTextBox_Log.Clear(); })); // 在UI线程清空日志显示框
                ReSetTestItemStatus(); // 重置所有测试项的状态显示
                ShowTestStatus("RUN"); // 更新测试状态为运行中

                // 构建并显示测试开始信息
                string tips = string.Empty;
                tips += $"开始测试{TestDevice.Sn}{Environment.NewLine}";
                tips += $"当前测试项目:{TestConfigBsj.Project}{Environment.NewLine}";
                tips += $"测试参数版本:{TestConfigBsj.Version}{Environment.NewLine}";
                tips += $"参数更新时间:{TestConfigBsj.UpdateTime}{Environment.NewLine}";
                ShowLog(tips);

                // 启动测试时间计时器
                Invoke(new Action(() => { timer_ShowTime.Start(); }));

                // 遍历所有测试项组，按顺序执行测试
                foreach (TestItem testItem in TestConfigBsj.Data)
                {
                    // 处理需要人工操作的测试项，显示操作提示对话框
                    if (testItem.Name.Contains("已佩戴检测"))
                    {
                        this.ShowWarningDialog("请把头盔佩戴在头模上,然后按确定键继续测试.");
                    }
                    else if (testItem.Name.Contains("对空标定"))
                    {
                        this.ShowWarningDialog("请把头盔从头模上取下,然后按确定键继续测试.");
                    }

                    // 实现测试项的重试机制
                    for (int retryIndex = 1; retryIndex <= testItem.Retry; retryIndex++)
                    {
                        // 检查是否收到取消测试的信号
                        if (Cts.IsCancellationRequested)
                        {
                            return; // 立即退出测试流程
                        }

                        // 跳过所有参数都被禁用的测试项组
                        if (!testItem.Params.Any(param => param.IsEnabled))
                        {
                            break; // 跳出重试循环，进入下一个测试项组
                        }

                        // 在UI线程中显示当前测试项组的开始信息
                        Invoke(new Action(() =>
                        {
                            // 找到当前测试项组中第一个启用的参数
                            TestItemParameter firstEnabledParam = testItem.Params.FirstOrDefault(x => x.IsEnabled);
                            if (firstEnabledParam != null)
                            {
                                // 获取该参数在ListView中的索引位置
                                int index = listView_TestItems.Items.IndexOf(listView_TestItems.Items[firstEnabledParam.Name]) + 1;
                                tips = Environment.NewLine + Environment.NewLine;
                                tips += $"▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼{Environment.NewLine}";
                                tips += $"{Ai.中括号左}{DateTime.Now:yyyy-MM-dd HH:mm:ss:fff}{Ai.中括号右}";
                                tips += $"开始检测{Ai.中括号左}{index}.{firstEnabledParam.Name}{Ai.中括号右}";
                                ShowLog(tips);
                            }
                        }));

                        // 执行测试项配置的延时等待
                        if (testItem.SleepTime > 0)
                        {
                            ShowLog($@"该项需要延时{Ai.中括号左}{testItem.SleepTime}{Ai.中括号右}毫秒");
                            await Task.Delay(testItem.SleepTime, Cts.Token); // 异步延时，支持取消
                            ShowLog($@"此处已延时了:{Ai.中括号左}{testItem.SleepTime}{Ai.中括号右}毫秒");
                        }

                        // 预处理测试指令，支持动态参数替换
                        string tmpCmdStr = testItem.CmdStr;
                        if (testItem.Name.Contains("连接蓝牙设备"))
                        {
                            // 将指令中的MAC地址占位符替换为实际的MAC地址
                            tmpCmdStr = tmpCmdStr.Replace("[mac]", $"{TmpMac}");
                        }
                        else if (testItem.Name.Equals("写入头盔ID"))
                        {
                            // 将指令中的ID占位符替换为实际的设备SN
                            tmpCmdStr = tmpCmdStr.Replace("[ID]", TestDevice.Sn);
                        }

                        // 根据配置决定是否清除之前接收的数据
                        if (testItem.ClearData)
                        {
                            SbDevice.Clear(); // 清空设备数据缓冲区
                        }

                        // 发送测试指令到设备（如果指令不为空）
                        if (!string.IsNullOrWhiteSpace(tmpCmdStr))
                        {
                            SerialPortWriteDevice(tmpCmdStr, testItem.AsciiSend, testItem.EnterLine);
                        }

                        // 处理特殊的MES过站测试项
                        if (testItem.Name.Contains("MES"))
                        {
                            // 调用MES系统进行过站操作：SN号、工序、用户账号、状态
                            CallBackResultModel<string> result = MESService.PassStation(
                                TestDevice.Sn,
                                Setting.Current.StationName,
                                Setting.Current.UserName,
                                true,
                                "PASS");

                            // 将MES返回结果添加到数据缓冲区
                            SbDevice.AppendLine(result.Success.ToString());
                            SbDevice.AppendLine(result.Msg);
                            SbDevice.AppendLine(result.Data);
                            // 显示MES操作结果
                            tips = result.Success + Environment.NewLine;
                            tips += result.Msg + Environment.NewLine;
                            tips += result.Data + Environment.NewLine;
                            ShowLog(tips);
                        }

                        // 等待并监测设备返回数据
                        string recData = string.Empty;
                        for (int j = 1; j <= testItem.WaitCount; j++)
                        {
                            // 按配置的等待时间间隔检查数据
                            Thread.Sleep(testItem.WaitTime);

                            // 检查是否收到取消测试的信号
                            if (Cts.IsCancellationRequested)
                            {
                                return; // 立即退出测试流程
                            }

                            // 检查设备数据缓冲区是否有数据
                            if (SbDevice.Length <= 0)
                            {
                                continue; // 没有数据，继续等待
                            }

                            // 获取当前接收到的所有数据
                            recData = SbDevice.ToString().Trim();

                            // 检查返回数据是否包含预期的结束标识字符串
                            if (recData.Contains(testItem.EndWithStr))
                            {
                                break; // 接收到完整数据，跳出等待循环
                            }
                        }

                        // 检查是否成功接收到测试数据
                        if (string.IsNullOrWhiteSpace(recData.Trim()))
                        {
                            // 如果已经是最后一次重试，则标记测试失败
                            if (retryIndex.Equals(testItem.Retry))
                            {
                                ShowLog($@"未检测到测试组{Ai.中括号左}{testItem.Name}{Ai.中括号右}数据,请核对检查!", LogType.Error);
                                SetTestItemStatus(testItem.Params.First(x => x.IsEnabled).Name, EnumTestItemResult.Fail);
                                return; // 测试失败，退出整个测试流程
                            }

                            continue; // 不是最后一次重试，继续重试
                        }

                        // ===== 开始遍历当前测试项组中的所有测试参数 =====
                        foreach (TestItemParameter param in testItem.Params)
                        {
                            // 检查是否收到取消测试的信号
                            if (Cts.IsCancellationRequested)
                            {
                                return; // 立即退出测试流程
                            }

                            // 跳过被禁用的测试参数
                            if (!param.IsEnabled)
                            {
                                continue;
                            }

                            // 显示测试前的提示信息（如果配置了的话）
                            if (!string.IsNullOrWhiteSpace(param.ShowInfoBefore))
                            {
                                ShowLog(param.ShowInfoBefore, LogType.Error);
                            }

                            // 如果当前测试参数不是该组中第一个启用的参数，则显示当前测试项信息
                            if (!param.Name.Equals(testItem.Params.First(x => x.IsEnabled).Name))
                            {
                                // 在UI线程中显示当前测试参数的开始信息
                                Invoke(new Action(() =>
                                {
                                    int index = listView_TestItems.Items.IndexOf(listView_TestItems.Items[param.Name]) + 1;
                                    tips = Environment.NewLine + Environment.NewLine;
                                    tips += $"▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼{Environment.NewLine}";
                                    tips += $"{Ai.中括号左}{DateTime.Now:yyyy-MM-dd HH:mm:ss:fff}{Ai.中括号右}";
                                    tips += $"开始检测{Ai.中括号左}{index}.{param.Name}{Ai.中括号右}";
                                    ShowLog(tips);
                                }));
                            }

                            // 创建单项测试计时器
                            Stopwatch stopwatchSingleItem = new Stopwatch();
                            stopwatchSingleItem.Start();

                            // 初始化测试结果变量
                            bool isPass = false; // 测试是否通过标志
                            string tmpGetTargetVal; // 从设备数据中提取的目标值
                            string tmpTargetValue = param.TargetValue; // 预期的目标值

                            // 处理动态目标值（某些测试项的目标值需要根据实际情况动态设置）
                            if (param.Name.Equals("读取头盔ID"))
                            {
                                tmpTargetValue = TestDevice.Sn; // 读取头盔ID的目标值应该是设备的SN
                            }

                            // 准备用于数据提取的正则表达式
                            string regexPattern = param.RegexPattern;
                            if (param.Name.Equals("扫描设备RSSI"))
                            {
                                regexPattern = regexPattern.Replace("[mac]", TmpMac);
                            }
                            else if (param.Name.Equals("读取头盔ID"))
                            {
                                regexPattern = regexPattern.Replace("[ID]", TestDevice.Sn);
                            }

                            // 先通过正则获取关键参数值
                            Regex regex = new Regex(regexPattern);
                            MatchCollection matches = regex.Matches(recData);
                            if (matches.Count > 0)
                            {
                                tmpGetTargetVal = matches[matches.Count - 1].Value.Trim();
                                // ShowLog($"正则匹配到数据:{tmpGetTargetVal}", LogType.Info);
                                // tips = $"正则匹配到数据:{tmpGetTargetVal}";
                                if (param.Name.Equals("三轴数据Z"))
                                {
                                    tmpGetTargetVal = Ai.GetTextRight(tmpGetTargetVal, ",");
                                    tmpGetTargetVal = Ai.GetTextRight(tmpGetTargetVal, ","); // 需要再次截取右边两位
                                }
                            }
                            else
                            {
                                // 没有匹配到任何数据,且未达到重试次数,则跳出循环再次重试
                                if (retryIndex < testItem.Retry)
                                {
                                    break;
                                }

                                // 已达到重试次数,则输出失败信息,终止所有测试
                                ShowLog($@"未匹配到{Ai.中括号左}{param.Name}{Ai.中括号右}数据,请核对检查!", LogType.Error);
                                SetTestItemStatus(param.Name, EnumTestItemResult.Fail);
                                return;
                            }

                            // 净化数据
                            if (param.CutTextType.Equals(EnumCutTextType.左边))
                            {
                                // ShowLog($"{Environment.NewLine}截取{Ai.中括号左}{param.CutTextFlag1}{Ai.中括号右}左边的数据");
                                tmpGetTargetVal = Ai.GetTextLeft(tmpGetTargetVal, param.CutTextFlag1).Trim();
                            }
                            else if (param.CutTextType.Equals(EnumCutTextType.右边))
                            {
                                // ShowLog($"{Environment.NewLine}截取{Ai.中括号左}{param.CutTextFlag1}{Ai.中括号右}右边的数据");
                                tmpGetTargetVal = Ai.GetTextRight(tmpGetTargetVal, param.CutTextFlag1).Trim();
                            }
                            else if (param.CutTextType.Equals(EnumCutTextType.中间))
                            {
                                // ShowLog($"{Environment.NewLine}截取{Ai.中括号左}{param.CutTextFlag1}{Ai.中括号右}与{Ai.中括号左}{param.CutTextFlag2}{Ai.中括号右}中间的数据");
                                tmpGetTargetVal = Ai.GetTextMiddle(tmpGetTargetVal, param.CutTextFlag1, param.CutTextFlag2).Trim();
                            }

                            // 输出净化后的数据
                            // ShowLog($"{Environment.NewLine}数据净化结果:{Ai.中括号左}{tmpGetTargetVal}{Ai.中括号右}");

                            // 判断是否为比较文本
                            if (param.IsCompareText)
                            {
                                // 对比目标值
                                if (param.CompareTextType.Equals(EnumCompareTextType.完全一致))
                                {
                                    isPass = tmpGetTargetVal.Equals(tmpTargetValue);
                                }
                                else if (param.CompareTextType.Equals(EnumCompareTextType.目标值包含返回值))
                                {
                                    isPass = tmpTargetValue.Contains(tmpGetTargetVal);
                                }
                                else if (param.CompareTextType.Equals(EnumCompareTextType.返回值包含目标值))
                                {
                                    isPass = tmpGetTargetVal.Contains(tmpTargetValue);
                                }
                                else if (param.CompareTextType.Equals(EnumCompareTextType.非空))
                                {
                                    isPass = !string.IsNullOrWhiteSpace(tmpGetTargetVal);

                                    // 非空情况下需要提取必要信息
                                    if (param.Name.Contains("读取蓝牙地址"))
                                    {
                                        tmpGetTargetVal = tmpGetTargetVal.Replace(":", "");
                                        ShowLog($@"读取到蓝牙地址:{tmpGetTargetVal}", LogType.Info);

                                        // 记录到数据库
                                        if (IsDbConnect)
                                        {
                                            BsjDeviceInfo info = new BsjDeviceInfo
                                            {
                                                DeviceSn = TestDevice.Sn,
                                                Name = "MAC",
                                                Value = tmpGetTargetVal,
                                                Project = TestDevice.Project,
                                                TestId = TestDevice.TestId,
                                                TestTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff"),
                                            };

                                            await DbHelper.FSql.Insert<BsjDeviceInfo>().AppendData(info).ExecuteAffrowsAsync();
                                        }
                                    }
                                    // else if (param.Name.Contains("读取蓝牙MAC"))
                                    // {
                                    //     ShowLog($@"提取到蓝牙地址:{tmpGetTargetVal}", LogType.Info);
                                    //
                                    //     // string mac = string.Empty;
                                    //     // for (int i = 0; i < 6; i++)
                                    //     // {
                                    //     //     mac += tmpTargetVal.Substring(tmpTargetVal.Length - 2, 2);
                                    //     //     tmpTargetVal = tmpTargetVal.Substring(0, tmpTargetVal.Length - 2);
                                    //     // }
                                    //
                                    //     TestDevice.Mac = tmpGetTargetVal;
                                    //     // ShowLog($@"翻转后蓝牙地址:{TestDevice.Mac}", LogType.Info);
                                    // }
                                    // else if (param.Name.Contains("IMEI读取保存"))
                                    // {
                                    //     TestDevice.Imei = tmpGetTargetVal;
                                    //     ShowLog($@"提取到IMEI:{tmpGetTargetVal}", LogType.Info);
                                    // }
                                    // else if (param.Name.Contains("IMSI读取保存"))
                                    // {
                                    //     TestDevice.Imsi = tmpGetTargetVal;
                                    //     ShowLog($@"提取到IMSI:{tmpGetTargetVal}", LogType.Info);
                                    // }
                                    // else if (param.Name.Contains("ICCID读取保存"))
                                    // {
                                    //     TestDevice.IccId = tmpGetTargetVal;
                                    //     ShowLog($@"提取到ICCID:{tmpGetTargetVal}", LogType.Info);
                                    // }
                                    // else if (param.Name.Contains("蓝牙软件版本"))
                                    // {
                                    //     TestDevice.BleVersion = tmpGetTargetVal;
                                    //     ShowLog($@"提取到BleVersion:{tmpGetTargetVal}", LogType.Info);
                                    // }
                                    // else if (param.Name.Contains("硬件版本"))
                                    // {
                                    //     TestDevice.HwVersion = tmpGetTargetVal;
                                    //     ShowLog($@"提取到硬件版本:{tmpGetTargetVal}", LogType.Info);
                                    // }
                                    // else if (param.Name.Contains("软件版本"))
                                    // {
                                    //     TestDevice.FwVersion = tmpGetTargetVal;
                                    //     ShowLog($@"提取到软件版本:{tmpGetTargetVal}", LogType.Info);
                                    // }
                                }
                            }
                            else
                            {
                                double.TryParse(tmpGetTargetVal, out double resVal);
                                // ShowLog($"转换数值:{Ai.中括号左}{resVal}{Ai.中括号右}");

                                if (!param.RestoreBase.Equals(1))
                                {
                                    resVal *= param.RestoreBase;

                                    ShowLog($"原始值:{Ai.中括号左}{tmpGetTargetVal}{Ai.中括号右},还原基数:{Ai.中括号左}{param.RestoreBase}{Ai.中括号右}, 还原值:{Ai.中括号左}{resVal}{Ai.中括号右}", LogType.Info);
                                    tmpGetTargetVal = resVal.ToString(CultureInfo.InvariantCulture);
                                }

                                isPass = resVal >= param.MinValue && resVal <= param.MaxValue;
                            }

                            // 判断写入数据库
                            if (IsDbConnect)
                            {
                                // 创建数据库明细对象
                                TestDetail detail = new TestDetail
                                {
                                    ProjectName = TestConfigBsj.Project,
                                    TestId = TestDevice.TestId,
                                    Sn = TestDevice.Sn,
                                    Name = param.Name,
                                    Status = isPass ? "PASS" : "NG",
                                    Val = tmpGetTargetVal,
                                    RefMinVal = param.MinValue.ToString(CultureInfo.InvariantCulture),
                                    RefMaxVal = param.MaxValue.ToString(CultureInfo.InvariantCulture)
                                };

                                // 对比文本的情况下,需要将目标值写入数据库
                                if (param.IsCompareText)
                                {
                                    detail.RefMinVal = tmpTargetValue;
                                }

                                // 将测试明细写入数据库
                                detail.Id = await DbHelper.FSql.Insert<TestDetail>()
                                    .AppendData(detail)
                                    .ExecuteIdentityAsync();
                            }

                            // 输出失败信息
                            if (!isPass)
                            {
                                // 如果没达到重试次数,则跳出循环再次重试
                                if (retryIndex < testItem.Retry)
                                {
                                    break;
                                }

                                // 已达到重试次数,则输出失败信息,结束所有测试
                                string errorMsg = param.IsCompareText
                                    ? $@"{Ai.中括号左}{param.Name}{Ai.中括号右}失败,返回值{Ai.中括号左}{tmpGetTargetVal}{param.Unit}{Ai.中括号右}与目标值{Ai.中括号左}{param.TargetValue}{param.Unit}{Ai.中括号右}不匹配!"
                                    : $@"{Ai.中括号左}{param.Name}{Ai.中括号右}失败,返回值{Ai.中括号左}{tmpGetTargetVal}{param.Unit}{Ai.中括号右}与目标值{Ai.中括号左}{param.MinValue}{param.Unit}{Ai.中括号右}至{Ai.中括号左}{param.MaxValue}{param.Unit}{Ai.中括号右}不匹配!";

                                // 输出失败信息
                                ShowLog(errorMsg, LogType.Error);
                                SetTestItemStatus(param.Name, EnumTestItemResult.Fail);
                                return;
                            }

                            // 输出成功信息
                            if (param.IsCompareText)
                            {
                                switch (param.CompareTextType)
                                {
                                    case EnumCompareTextType.完全一致:
                                        ShowLog($@"{Ai.中括号左}{param.Name}{param.Unit}{Ai.中括号右}通过,返回值{Ai.中括号左}{tmpGetTargetVal}{Ai.中括号右}与目标值{Ai.中括号左}{tmpTargetValue}{Ai.中括号右}完全一致。", LogType.Info);
                                        break;
                                    case EnumCompareTextType.目标值包含返回值:
                                        ShowLog($@"{Ai.中括号左}{param.Name}{param.Unit}{Ai.中括号右}通过,目标值{Ai.中括号左}{tmpTargetValue}{Ai.中括号右}包含返回值{Ai.中括号左}{tmpGetTargetVal}{Ai.中括号右}。", LogType.Info);
                                        break;
                                    case EnumCompareTextType.返回值包含目标值:
                                        ShowLog($@"{Ai.中括号左}{param.Name}{param.Unit}{Ai.中括号右}通过,返回值{Ai.中括号左}{tmpGetTargetVal}{Ai.中括号右}包含目标值{Ai.中括号左}{tmpTargetValue}{Ai.中括号右}。", LogType.Info);
                                        break;
                                    case EnumCompareTextType.非空:
                                        ShowLog($@"{Ai.中括号左}{param.Name}{param.Unit}{Ai.中括号右}通过,返回值{Ai.中括号左}{tmpGetTargetVal}{Ai.中括号右}", LogType.Info);
                                        break;
                                }
                            }
                            else
                            {
                                ShowLog($@"{Ai.中括号左}{param.Name}{Ai.中括号右}通过,{Ai.中括号左}{tmpGetTargetVal}{param.Unit}{Ai.中括号右}符合目标值范围{Ai.中括号左}{param.MinValue}{param.Unit}{Ai.中括号右}至{Ai.中括号左}{param.MaxValue}{param.Unit}{Ai.中括号右}", LogType.Info);
                            }

                            // 显示检测结果
                            SetTestItemStatus(param.Name, EnumTestItemResult.Pass);

                            // 显示检测后提示信息
                            if (!string.IsNullOrWhiteSpace(param.ShowInfoAfter))
                            {
                                ShowLog(param.ShowInfoAfter, LogType.Error);
                            }

                            // 显示测试项备注信息
                            if (!string.IsNullOrWhiteSpace(param.Remark))
                            {
                                ShowLog(param.Remark, LogType.Error);
                            }

                            // 获取单项检测耗时
                            stopwatchSingleItem.Stop();
                            if (!TestItemTimeDic.ContainsKey(param.Name))
                            {
                                TestItemTimeDic.Add(param.Name, stopwatchSingleItem.ElapsedMilliseconds);
                            }

                            // 判断是否为最后一个需要检测的参数,如果是,则跳出循环进行下一组测试
                            if (param.Name.Equals(testItem.Params.Last(p => p.IsEnabled).Name))
                            {
                                retryIndex = 999;
                            }
                        }
                    }

                    // 显示测试组备注信息
                    if (!string.IsNullOrWhiteSpace(testItem.Remark))
                    {
                        ShowLog(testItem.Remark, LogType.Error);
                    }
                }

                // 设置状态
                TestDevice.Pass = true;
            }
            catch (Exception ex)
            {
                // 输出异常信息
                ShowLog(ex.ToString(), LogType.Error);
            }
            finally
            {
                ShowLog($"#################### {Ai.中括号左}测试结束{Ai.中括号右} ####################", LogType.Error);
                Invoke(new Action(() => { timer_ShowTime.Stop(); }));
                TestDevice.TakeTime = TestTime;

                // 判断是否为手动取消
                if (Cts.IsCancellationRequested)
                {
                    ShowLog("测试被取消", LogType.Error);
                }

                // 善后处理
                SerialPortWriteDevice("AT+DISCON", enterLine: false);
                Thread.Sleep(200);
                SerialPortWriteDevice("AT+RESET", enterLine: false);

                // 显示测试结果
                ShowTestStatus(TestDevice.Pass ? "PASS" : "FAIL");

                // 将测试设备记录到数据库
                if (IsDbConnect)
                {
                    await DbHelper.FSql.Insert<BsjDevice>()
                        .AppendData(TestDevice)
                        .ExecuteIdentityAsync();
                }

                // 确定日志文件名
                string logFileName = $"{TestDevice.Sn}_{DateTime.Now:yyyyMMddHHmmss}.txt";
                if (!TestDevice.Pass)
                {
                    logFileName = $"{TestDevice.Sn}_{DateTime.Now:yyyyMMddHHmmss}_FAIL.txt";
                }

                // 写出测试Log到文件
                string logFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs", logFileName);
                Invoke(new Action(() => { File.WriteAllText(logFilePath, uiRichTextBox_Log.Text); }));

                // 显示本轮整体检测耗时,并提示下一轮测试
                ShowLog(@"本轮整体检测耗时: " + Ai.中括号左 + TestTime + Ai.中括号右 + " 秒", LogType.Info);
                ShowLog("请将产品从治具取出,然后扫描下一个SN码开始新一轮检测.", LogType.Error);

                // 测试结束,重置相关参数值
                Cts.Cancel();
                TestDevice = null;
            }
        }

        #endregion

        #region 设置检测状态

        private void ReSetTestItemStatus()
        {
            Invoke(new Action(() =>
            {
                foreach (ListViewItem item in listView_TestItems.Items)
                {
                    item.ImageIndex = 0;
                }
            }));
        }

        private void SetTestItemStatus(string key, EnumTestItemResult result)
        {
            Invoke(new Action(() =>
            {
                listView_TestItems.Items[key].ImageIndex = (int)result;
                listView_TestItems.EnsureVisible(listView_TestItems.Items[key].Index);
            }));
        }

        private void ShowTestStatus(string resultCode)
        {
            Invoke(new Action(() =>
            {
                uiLabel_Status.Text = resultCode;
                switch (resultCode)
                {
                    case "WAIT":
                        uiLabel_Status.BackColor = Color.LightGray;
                        break;
                    case "RUN":
                        uiLabel_Status.BackColor = Color.LightYellow;
                        break;
                    case "PASS":
                        uiLabel_Status.BackColor = Color.Green;
                        break;
                    default:
                        uiLabel_Status.BackColor = Color.Red;
                        break;
                }
            }));
        }

        #endregion

        #region 输出日志

        private void ShowLog(string logStr, LogType logType = LogType.Message, bool enterLine = true)
        {
            // 不处理空值
            if (string.IsNullOrWhiteSpace(logStr))
            {
                return;
            }

            // 添加到日志队列
            _logQueue.Enqueue(new CustomLog() { LogStr = logStr, LogType = logType, EnterLine = enterLine });
        }

        private void InitializeLogTimer()
        {
            _logTimer = new Timer { Interval = 250 }; // 每250ms处理一次
            _logTimer.Tick += (sender, e) => ProcessLogQueue();
            _logTimer.Start();
        }

        private void ProcessLogQueue()
        {
            while (_logQueue.TryDequeue(out CustomLog customLog))
            {
                // 设置uiRichTextBox_Log的颜色
                Color color = Color.Black;
                switch (customLog.LogType)
                {
                    case LogType.Info:
                        color = Color.Green;
                        break;
                    case LogType.Error:
                        color = Color.Red;
                        break;
                    case LogType.Warning:
                        color = Color.Orange;
                        break;
                }

                uiRichTextBox_Log.SelectionColor = color;

                // 设置uiRichTextBox_Log的字体
                Font font = new Font("微软雅黑", 10);
                if (customLog.LogType == LogType.Error || customLog.LogType == LogType.Info)
                {
                    font = new Font("微软雅黑", 10, FontStyle.Bold);
                }
                else if (customLog.LogType == LogType.Warning)
                {
                    font = new Font("微软雅黑", 10, FontStyle.Italic);
                }
                else if (customLog.LogStr.Contains(Ai.中括号左) && customLog.LogStr.Contains("开始检测") && customLog.LogStr.Contains(Ai.中括号右))
                {
                    font = new Font("微软雅黑", 10, FontStyle.Bold);
                }

                uiRichTextBox_Log.SelectionFont = font; // 设置字体

                // 处理logStr中的空白行
                customLog.LogStr = RemoveConsecutiveBlankLines(customLog.LogStr);
                if (customLog.EnterLine)
                {
                    customLog.LogStr += Environment.NewLine;
                }

                // 添加日志信息,并自动滚动到最后
                uiRichTextBox_Log.AppendText(customLog.LogStr);
                uiRichTextBox_Log.ScrollToCaret();
            }
        }

        private string RemoveConsecutiveBlankLines(string text)
        {
            // 使用正则表达式替换多行连续空白行
            return Regex.Replace(text, @"(\r\n|\r|\n){2,}", "\r\n");
        }

        #endregion
    }
}